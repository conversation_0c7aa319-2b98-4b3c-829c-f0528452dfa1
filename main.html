<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI प्रॉम्प्ट ऑप्टिमाइज़ेशन रिपोर्ट</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'hindi': ['Noto Sans Devanagari', 'serif']
                    },
                    colors: {
                        primary: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            900: '#134e4a'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'scale-in': 'scaleIn 0.2s ease-out'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .content-section {
            display: none;
            animation: fade-in 0.5s ease-in-out;
        }

        .content-section.active {
            display: block;
        }

        .nav-tab {
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #14b8a6, #0d9488);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-tab.active::before {
            width: 100%;
        }

        .prompt-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid transparent;
        }

        .prompt-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #14b8a6;
        }

        .highlight-new {
            background: linear-gradient(120deg, #a7f3d0 0%, #6ee7b7 100%);
            padding: 2px 6px;
            border-radius: 6px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(20, 184, 166, 0.2);
        }

        .filter-btn {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .filter-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .filter-btn:hover::before {
            left: 100%;
        }

        .chart-container {
            position: relative;
            height: 350px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .section-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .principle-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(20, 184, 166, 0.2);
            transition: all 0.3s ease;
        }

        .principle-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(20, 184, 166, 0.15);
            border-color: rgba(20, 184, 166, 0.4);
        }

        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        .text-gradient {
            background: linear-gradient(135deg, #0f766e 0%, #14b8a6 50%, #5eead4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="min-h-screen gradient-bg">
    <!-- Background Pattern -->
    <div class="fixed inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #14b8a6 0%, transparent 50%), radial-gradient(circle at 75% 75%, #0d9488 0%, transparent 50%);"></div>
    </div>

    <div id="app" class="relative z-10 min-h-screen">
        <!-- Header -->
        <header class="text-center py-16 px-4">
            <div class="max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 font-hindi animate-fade-in">
                    AI प्रॉम्प्ट ऑप्टिमाइज़ेशन
                </h1>
                <div class="w-24 h-1 bg-gradient-to-r from-primary-100 to-white mx-auto mb-6 rounded-full"></div>
                <p class="text-xl md:text-2xl text-white/90 font-light max-w-2xl mx-auto leading-relaxed">
                    एआई-जनित छवियों और वीडियो के लिए प्रॉम्प्ट को बेहतर बनाने का आधुनिक समाधान
                </p>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="sticky top-0 z-50 backdrop-blur-md bg-white/10 border-b border-white/20">
            <div class="max-w-6xl mx-auto px-4">
                <ul class="flex justify-center space-x-8 py-4">
                    <li>
                        <a href="#home" class="nav-tab active px-6 py-3 text-white/90 hover:text-white font-medium transition-all duration-300 font-hindi" data-target="home">
                            मुख्य पृष्ठ
                        </a>
                    </li>
                    <li>
                        <a href="#guide" class="nav-tab px-6 py-3 text-white/90 hover:text-white font-medium transition-all duration-300 font-hindi" data-target="guide">
                            सिद्धांत
                        </a>
                    </li>
                    <li>
                        <a href="#explorer" class="nav-tab px-6 py-3 text-white/90 hover:text-white font-medium transition-all duration-300 font-hindi" data-target="explorer">
                            प्रॉम्प्ट एक्सप्लोरर
                        </a>
                    </li>
                    <li>
                        <a href="#learnings" class="nav-tab px-6 py-3 text-white/90 hover:text-white font-medium transition-all duration-300 font-hindi" data-target="learnings">
                            मुख्य सीख
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 py-12">
            <!-- Home Section -->
            <section id="home" class="content-section active">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                    <!-- Introduction Card -->
                    <div class="section-card rounded-3xl p-8 animate-slide-up">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h2 class="text-3xl font-bold text-gradient font-hindi">रिपोर्ट का परिचय</h2>
                        </div>
                        <p class="text-slate-700 leading-relaxed font-hindi text-lg">
                            यह रिपोर्ट एआई-आधारित छवि और वीडियो जनरेशन के लिए प्रॉम्प्ट्स के गहन विश्लेषण और अनुकूलन पर केंद्रित है।
                            हमारा लक्ष्य प्रॉम्प्ट्स को "पूरी तरह से अनुकूलित" करना है ताकि वे एआई मॉडल्स से उच्च-गुणवत्ता वाले आउटपुट उत्पन्न करें।
                        </p>
                        <div class="mt-6 flex flex-wrap gap-3">
                            <span class="px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">विश्लेषण</span>
                            <span class="px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">अनुकूलन</span>
                            <span class="px-4 py-2 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">तुलना</span>
                        </div>
                    </div>

                    <!-- Stats Card -->
                    <div class="section-card rounded-3xl p-8 animate-slide-up" style="animation-delay: 0.1s;">
                        <h3 class="text-2xl font-bold text-gradient mb-6 font-hindi">डेटा अवलोकन</h3>
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center">
                                <div class="text-4xl font-bold text-primary-600 mb-2">20</div>
                                <div class="text-slate-600 font-hindi">कुल प्रॉम्प्ट्स</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-primary-600 mb-2">2</div>
                                <div class="text-slate-600 font-hindi">मुख्य श्रेणियां</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-primary-600 mb-2">100%</div>
                                <div class="text-slate-600 font-hindi">अनुकूलन दर</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold text-primary-600 mb-2">AI</div>
                                <div class="text-slate-600 font-hindi">संचालित</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart Section -->
                <div class="section-card rounded-3xl p-8 animate-slide-up" style="animation-delay: 0.2s;">
                    <div class="flex items-center mb-8">
                        <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-gradient font-hindi">प्रॉम्प्ट श्रेणियों का वितरण</h3>
                            <p class="text-slate-600 font-hindi mt-1">श्रेणियों का पता लगाने के लिए चार्ट पर क्लिक करें</p>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Guide Section -->
            <section id="guide" class="content-section">
                <div class="section-card rounded-3xl p-8 animate-slide-up">
                    <div class="flex items-center mb-8">
                        <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gradient font-hindi">प्रभावी प्रॉम्प्ट इंजीनियरिंग के सिद्धांत</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.1s;">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <h3 class="font-bold text-xl text-slate-800 font-hindi">मुख्य घटक</h3>
                            </div>
                            <p class="text-slate-600 font-hindi leading-relaxed">
                                एक प्रभावी प्रॉम्प्ट में स्पष्ट घटक होने चाहिए: विषय, क्रिया, वातावरण, माध्यम/शैली, प्रकाश, रंग, मनोदशा और संरचना।
                            </p>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.2s;">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="font-bold text-xl text-slate-800 font-hindi">विशिष्टता का महत्व</h3>
                            </div>
                            <p class="text-slate-600 font-hindi leading-relaxed">
                                जितना अधिक विशिष्ट और विस्तृत प्रॉम्प्ट होगा, एआई मॉडल द्वारा वांछित परिणाम उत्पन्न करने की संभावना उतनी ही अधिक होगी।
                            </p>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.3s;">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="font-bold text-xl text-slate-800 font-hindi">तकनीकी पैरामीटर्स</h3>
                            </div>
                            <p class="text-slate-600 font-hindi leading-relaxed">
                                8K UHD, DSLR 85mm लेंस, सिनेमैटिक कलर ग्रेडिंग जैसे तकनीकी विवरण शामिल करने से गुणवत्ता में काफी सुधार होता है।
                            </p>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.4s;">
                            <div class="flex items-center mb-4">
                                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L12 12m6.364 6.364L12 12m0 0L5.636 5.636M12 12l6.364-6.364M12 12l-6.364 6.364"></path>
                                    </svg>
                                </div>
                                <h3 class="font-bold text-xl text-slate-800 font-hindi">नकारात्मक प्रॉम्प्ट्स</h3>
                            </div>
                            <p class="text-slate-600 font-hindi leading-relaxed">
                                अवांछित तत्वों को बाहर करने के लिए नकारात्मक प्रॉम्प्ट्स का उपयोग करें। यह आउटपुट को परिष्कृत करने में मदद करता है।
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Explorer Section -->
            <section id="explorer" class="content-section">
                <div class="section-card rounded-3xl p-8 animate-slide-up">
                    <div class="flex items-center mb-8">
                        <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-3xl font-bold text-gradient font-hindi">प्रॉम्प्ट एक्सप्लोरर</h2>
                            <p class="text-slate-600 font-hindi mt-1">श्रेणी के अनुसार प्रॉम्प्ट्स को फ़िल्टर करें और तुलना करें</p>
                        </div>
                    </div>

                    <!-- Filter Buttons -->
                    <div id="filter-buttons" class="flex flex-wrap justify-center gap-3 mb-8">
                        <!-- Filter buttons will be generated here -->
                    </div>

                    <!-- Main Explorer Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
                        <!-- Prompt List -->
                        <div class="lg:col-span-2">
                            <div class="glass-effect rounded-2xl p-6">
                                <h3 class="text-xl font-bold text-slate-800 mb-4 font-hindi flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                    </svg>
                                    प्रॉम्प्ट्स
                                </h3>
                                <div id="prompt-list" class="space-y-3 max-h-[600px] overflow-y-auto scrollbar-hide">
                                    <!-- Prompt list will be generated here -->
                                </div>
                            </div>
                        </div>

                        <!-- Detail Panel -->
                        <div class="lg:col-span-3">
                            <div class="glass-effect rounded-2xl p-6 min-h-[600px]">
                                <div id="prompt-detail-placeholder" class="flex flex-col items-center justify-center h-full text-center">
                                    <div class="w-24 h-24 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center mb-6">
                                        <svg class="w-12 h-12 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-bold text-slate-700 mb-2 font-hindi">प्रॉम्प्ट चुनें</h3>
                                    <p class="text-slate-500 font-hindi">तुलना देखने के लिए बाईं ओर से एक प्रॉम्प्ट चुनें</p>
                                </div>

                                <div id="prompt-detail-content" class="hidden animate-fade-in">
                                    <h3 id="detail-title" class="text-2xl font-bold text-gradient mb-6 font-hindi"></h3>

                                    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-6">
                                        <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 border border-red-200">
                                            <h4 class="text-lg font-bold mb-3 text-red-700 font-hindi flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                                मूल प्रॉम्प्ट
                                            </h4>
                                            <div id="detail-original" class="text-sm text-slate-700 leading-relaxed font-hindi max-h-64 overflow-y-auto scrollbar-hide"></div>
                                        </div>

                                        <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200">
                                            <h4 class="text-lg font-bold mb-3 text-green-700 font-hindi flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                अनुकूलित प्रॉम्प्ट
                                            </h4>
                                            <div id="detail-optimized" class="text-sm text-slate-700 leading-relaxed font-hindi max-h-64 overflow-y-auto scrollbar-hide"></div>
                                        </div>
                                    </div>

                                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
                                        <h4 class="text-lg font-bold mb-3 text-blue-700 font-hindi flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            परिवर्तनों का औचित्य
                                        </h4>
                                        <div id="detail-justification" class="text-sm text-slate-700 leading-relaxed font-hindi"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Learnings Section -->
            <section id="learnings" class="content-section">
                <div class="section-card rounded-3xl p-8 animate-slide-up">
                    <div class="flex items-center mb-8">
                        <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h2 class="text-3xl font-bold text-gradient font-hindi">मुख्य सीख और सिफारिशें</h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.1s;">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-slate-800 mb-2 font-hindi">पुनरावृत्ति महत्वपूर्ण है</h3>
                                    <p class="text-slate-600 font-hindi leading-relaxed">वांछित परिणाम प्राप्त करने के लिए प्रॉम्प्ट्स को लगातार परिष्कृत करने की आवश्यकता होती है।</p>
                                </div>
                            </div>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.2s;">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-slate-800 mb-2 font-hindi">संक्षिप्तता बनाम विवरण</h3>
                                    <p class="text-slate-600 font-hindi leading-relaxed">एआई को स्पष्ट दिशा देने के लिए पर्याप्त विवरण प्रदान करें, लेकिन अनावश्यक शब्दों से बचें।</p>
                                </div>
                            </div>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.3s;">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-slate-800 mb-2 font-hindi">विशिष्ट शब्दावली</h3>
                                    <p class="text-slate-600 font-hindi leading-relaxed">सटीक पर्यायवाची और ठोस भाषा का उपयोग करने से एआई की व्याख्या क्षमता में काफी वृद्धि होती है।</p>
                                </div>
                            </div>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.4s;">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L12 12m6.364 6.364L12 12m0 0L5.636 5.636M12 12l6.364-6.364M12 12l-6.364 6.364"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-slate-800 mb-2 font-hindi">नकारात्मक प्रॉम्प्ट्स</h3>
                                    <p class="text-slate-600 font-hindi leading-relaxed">अवांछित तत्वों को फ़िल्टर करके आउटपुट को ठीक करने के लिए यह एक शक्तिशाली उपकरण है।</p>
                                </div>
                            </div>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.5s;">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-slate-800 mb-2 font-hindi">वीडियो बनाम छवि</h3>
                                    <p class="text-slate-600 font-hindi leading-relaxed">वीडियो जनरेशन के लिए कैमरा मूवमेंट और समय के साथ क्रियाओं जैसे अतिरिक्त तत्वों की आवश्यकता होती है।</p>
                                </div>
                            </div>
                        </div>

                        <div class="principle-card rounded-2xl p-6 animate-scale-in" style="animation-delay: 0.6s;">
                            <div class="flex items-start">
                                <div class="w-8 h-8 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-bold text-lg text-slate-800 mb-2 font-hindi">संरचना का प्रयोग करें</h3>
                                    <p class="text-slate-600 font-hindi leading-relaxed">एक स्पष्ट संरचना का पालन करने से एआई को प्रॉम्प्ट को बेहतर ढंग से समझने में मदद मिलती है।</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        const promptData = [
            // Category 1: सामान्य अंतरंगता और भावनात्मक संबंध (10 prompts)
            {
                id: 1,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "महल में कामुक आलिंगन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है... एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे... सेटिंग: भव्य महल कक्ष...",
                optimized: "आई-लेवल शॉट, थर्ड्स का नियम, 8K UHD, हाइपर-रियलिस्टिक, DSLR 85mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक कलर ग्रेडिंग, वॉल्यूमेट्रिक लाइटिंग, फोटोरियलिस्टिक, गतिशील कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी और मोती जैसी गुणवत्ता के साथ भीतर से चमक रही है, उसका चेहरा बेदाग चिकना और किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को बढ़ाता है। उसका व्यवहार गहरी शर्म और गहन आत्मनिरीक्षण का प्रतीक है, उसकी आँखें कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक कामुक खुशी का विकिरण करती हुई, गहन भावनात्मक संबंध को दर्शाती हुई। शानदार जेट-काले, रेशमी बाल, कूल्हों तक लंबे, एक सरल, बड़ा, और काला जूड़ा (उपडो) में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जो चेहरे की नाजुक विशेषताओं को और निखारते हैं। जूड़ा ताज़े चेरी ब्लॉसम और छोटे चांदी के हेयरपिन्स से सजा हुआ है। वह सूक्ष्म फूलों की कढ़ाई (चेरी ब्लॉसम, तितलियाँ) और छोटे सेक्विन के साथ एक बहती हुई पेस्टल लैवेंडर रेशमी शिफॉन पोशाक पहने हुए है। हाथ से सजे हुए बीज मोतियों के साथ पतला चांदी का शॉल। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे, उनके घुटने एक-दूसरे को हल्के से छूते हुए, उनके हाथ कामुकता से गूंथे हुए, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम, विश्वास और बढ़ती कामुकता को दर्शाता है। सेटिंग: गहरे लाल मखमली पर्दे के साथ भव्य महल कक्ष। इंद्रधनुषी प्रकाश बिखेरता हुआ शानदार क्रिस्टल झूमर। हवा में समृद्ध चंदन की तीव्र सुगंध, जो कामुकता को और गहरा करती है। मधुर शास्त्रीय बांसुरी की धुनें वातावरण में गूंजती हुई। कीमती पत्थरों और मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या।",
                justification: "वीडियो अनुकूलन के लिए 'गतिशील कैमरा पैनिंग' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक कलर ग्रेडिंग', 'वॉल्यूमेट्रिक लाइटिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। संवेदी अनुभव को तीव्र करने के लिए गंध और ध्वनि के विवरण को बढ़ाया गया।"
            },
            {
                id: 2,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "उत्सव का आलिंगन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष त्योहार के कमरे में आलिंगनबद्ध हैं...",
                optimized: "लो-एंगल शॉट, तीव्र विषय, धुंधली पृष्ठभूमि, 8K UHD, सिनेमैटिक कलर ग्रेडिंग, फोटोरियलिस्टिक, HDR, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान, गोरी है और आंतरिक चमक से जगमगा रही है, उसका चेहरा बेदाग चिकना और किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसका व्यवहार कोमल शर्म के साथ bubbling आनंद का मिश्रण दर्शाता है, उसकी आँखें शांत संतुष्टि में धीरे से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें धीमी, कामुक आहें हैं। उसके बाल घने, जेट-काले, रेशमी, कूल्हों तक लंबे, सीधे स्टाइल में हैं, जो एक तरफ कंधे से आगे की ओर छाती पर खूबसूरती से गिरे हुए हैं। वह जटिल चांदी के धागे की कढ़ाई (कमल, मोर के रूपांकन) और एक समृद्ध सुनहरी बॉर्डर के साथ एक उत्कृष्ट पन्ना हरे रंग की रेशमी साड़ी पहने हुए है। मैचिंग चांदी के धागे के काम और छोटे बीज मोतियों से heavily embroidered fitted blouse। एक शक्तिशाली रूप से पेशी वाले 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से लिपटे हुए, जो उनके पीछे खड़ा है, उसकी बाहें उनके कंधों पर कोमलता से लिपटी हुई हैं, उसके गाल उसके बालों को हल्के से छूते हैं, जो गहरे भावनात्मक बंधन और बढ़ती कामुकता का प्रतीक है। सेटिंग: त्योहार समारोह के लिए गर्म रोशनी वाला पारंपरिक कमरा। नरम, हाथ से बुना हुआ ऊनी कालीन (बरगंडी, सोने के पैटर्न)। पीतल की लालटेनों से गर्म एम्बर प्रकाश। कोमल, मधुर सितार की धुनें जो वातावरण को कामुक बनाती हैं। हवा में गुलाब जल की तीव्र सुगंध। गेंदे के फूलों की मालाएं और छोटे मिट्टी के दीये।",
                justification: "वीडियो के लिए 'धीमी कैमरा ट्रैकिंग' जोड़ा गया। प्रकाश और गहराई को बढ़ाने के लिए 'HDR' और 'वॉल्यूमेट्रिक लाइटिंग' को शामिल किया गया। भावनात्मक अभिव्यक्ति ('bubbling आनंद') और सेटिंग के विवरण (साड़ी, संगीत, सुगंध) को और अधिक विशिष्ट बनाया गया।"
            },
            {
                id: 3,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "पोनीटेल में कामुकता",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष महल में पोनीटेल के साथ बैठे हैं।",
                optimized: "आई-लेवल शॉट, थर्ड्स का नियम, 8K UHD, हाइपर-रियलिस्टिक, DSLR 85mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक लाइटिंग, फोटोरियलिस्टिक, धीमी ज़ूम-इन गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है, एक मोती जैसी गुणवत्ता के साथ अंदर से चमक रही है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को बढ़ाता है। उसका व्यवहार गहरी शर्म और गहन आत्मनिरीक्षण का प्रतीक है, उसकी आँखें कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक कामुक खुशी का विकिरण करती हुई, गहन भावनात्मक संबंध को दर्शाती हुई। शानदार जेट-काले घने सीधे बाल, कूल्हों तक लंबे, एक मोटी और लंबी पोनीटेल में जटिल रूप से बंधे हुए हैं, जो उसकी स्त्रैणता को और निखारते हैं। वह सूक्ष्म फूलों की कढ़ाई (चेरी ब्लॉसम, तितलियाँ) और छोटे सेक्विन के साथ एक बहती हुई पेस्टल लैवेंडर रेशमी शिफॉन पोशाक पहने हुए है। हाथ से सजे हुए बीज मोतियों के साथ पतला चांदी का शॉल। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे, उनके घुटने एक-दूसरे को हल्के से छूते हुए, उनके हाथ आपस में कामुकता से गूंथे हुए, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम, विश्वास और बढ़ती कामुकता को दर्शाता है। सेटिंग: गहरे लाल मखमली पर्दे के साथ भव्य महल कक्ष। इंद्रधनुषी प्रकाश बिखेरता हुआ शानदार क्रिस्टल झूमर। हवा में समृद्ध चंदन की तीव्र सुगंध, जो कामुकता को और गहरा करती है। मधुर शास्त्रीय बांसुरी की धुनें वातावरण में गूंजती हुई। कीमती पत्थरों और मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी ज़ूम-इन गति' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक लाइटिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। केश विन्यास का अधिक विस्तृत वर्णन, पोशाक के विवरण में वृद्धि, सुगंध और ध्वनि का विस्तार।"
            },
            {
                id: 4,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "वैवाहिक प्रत्याशा",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष शादी के कमरे में अंतरंगता से लेटे हुए हैं।",
                optimized: "ओवरहेड शॉट, नरम छाया, 8K UHD, सिनेमैटिक लाइटिंग, HDR, फोटोरियलिस्टिक, धीमी कैमरा रोटेशन। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए हाथी दांत की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और प्रत्याशा से चमक रहा है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसकी स्त्रैण सुंदरता को बढ़ाता है। उसका व्यवहार कोमल शर्म और शांत स्थिरता का मिश्रण दर्शाता है, उसकी आँखें परमानंद से धीरे से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें शुद्ध कामुक खुशी की हल्की आहें हैं, जो गहरे भावनात्मक बंधन और प्रत्याशा को दर्शाती है। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, एक हाई बन में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जो उसकी गर्दन की नाजुकता को उजागर करते हैं। वह बेहतरीन रेशम के एक लुभावने शाही नीले अनारकली सूट में सजे हुए है, जिसमें जटिल सुनहरे धागे की कढ़ाई (Paisley, कमल के रूपांकन) और नाजुक दर्पण का काम है। उसके सिर और कंधों पर भारी कढ़ाई वाला दुपट्टा। एक शक्तिशाली रूप से पेशी वाले 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ लेटे हुए, उनके शरीर एक-दूसरे के करीब, एक-दूसरे की बाहों में, उनके होंठ हल्के से एक-दूसरे को कामुकता से छूते हुए, जो गहरे प्रेम और अंतरंगता को दर्शाता है। सेटिंगः शादी समारोह के लिए तैयार किया गया अंतरंग रूप से रोशनी वाला पारंपरिक कमरा। दर्जनों पीतल के दीये गर्म, सुनहरी रोशनी बिखेर रहे हैं। एक जटिल नक्काशीदार लकड़ी के बेंच पर आलीशान रेशमी कुशन। हवा में ताजी गुलाब की पंखुड़ियों की तीव्र सुगंध, जो कामुक माहौल बनाती है। मधुर, रागपूर्ण तानपुरा की गूंज वातावरण में व्याप्त है। गेंदे के फूलों की मालाएं, चमेली के फूल, गुलाब जल और चंदन के पेस्ट के साथ पारंपरिक पीतल के बर्तन।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा रोटेशन' जोड़ा गया। प्रकाश और मनोदशा का सूक्ष्म विवरण, भावनात्मक स्थिति का स्पष्टीकरण। वस्त्र और आभूषण का विस्तृत वर्णन, प्रकाश व्यवस्था और संवेदी विवरण में सुधार।"
            },
            {
                id: 5,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "आधुनिक महल में कोमलता",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष आधुनिक महल में अंतरंगता से खड़े हैं।",
                optimized: "वाइड शॉट, नरम प्रकाश, 8K UHD, फोटोरियलिस्टिक, DSLR 35mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक लाइटिंग, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी है और अलौकिक कोमलता के साथ जगमगा रही है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसके स्त्रीत्व को और अधिक परिभाषित करता है। उसका व्यवहार गहरी शांति के साथ कोमल शर्म का मिश्रण दर्शाता है, जो शांत आनंद का विकिरण करता है। उसकी आँखें शांतिपूर्ण संतुष्टि में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें हल्का गुलाब-गुलाबी रंगत है, जो कोमल भावनात्मक संबंध और गहरे विश्वास को दर्शाता है। शानदार जेट-काले रेशमी बाल, टखने तक लंबे, घने, और स्ट्रेटनर से पूरी तरह सीधे किए हुए, कानों के पीछे व्यवस्थित रूप से रखे गए ताकि कान स्पष्ट रूप से दिखाई दें, जो उसके स्त्रीत्व को और अधिक उजागर करते हैं। वह बेहतरीन रेशमी शिफॉन के एक उत्कृष्ट नरम आड़ू रंग के बहते गाउन में सजे हुए है, जिसमें नाजुक फीते की कढ़ाई (फूलों के पैटर्न) और छोटे बीज मोती हैं। पतला सुनहरा शॉल। एक शक्तिशाली रूप से पेशी वाले 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से खड़े, उनके शरीर एक-दूसरे के खिलाफ कामुकता से दबे हुए, उनकी उंगलियाँ कोमलता से एक-दूसरे के चेहरे को सहलाती हुई, एक गहरे भावनात्मक बंधन और बढ़ी हुई कामुकता का प्रतीक है। सेटिंग: पारंपरिक भारतीय सौंदर्यशास्त्र को समकालीन विलासिता के साथ मिश्रित करते हुए शानदार ढंग से सजाया गया आधुनिक महल का कमरा। गहरे बैंगनी मखमली पर्दे। ताजे गुलाबों के साथ प्राचीन सफेद संगमरमर की मेज। कई खंभे वाली मोमबत्तियाँ नरम, गर्म रोशनी बिखेर रही हैं। हवा में नाजुक लैवेंडर की तीव्र सुगंध, जो कामुकता को बढ़ाती है। मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या, आधुनिक कला के टुकड़े।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक लाइटिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। वस्त्र और आभूषण का विस्तार, प्रकाश और बनावट पर जोर।"
            },
            {
                id: 6,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "लयबद्ध कामुक नृत्य",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष पारंपरिक कमरे में नृत्य कर रहे हैं।",
                optimized: "सिनेमैटिक ट्रैकिंग शॉट, गतिशील गति, 8K UHD, फोटोरियलिस्टिक, DSLR 100mm लेंस, HDRI, वॉल्यूमेट्रिक लाइटिंग, धीमी गति में नृत्य। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और आंतरिक चमक से दमक रही है, शुद्ध और मोती जैसी है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसकी स्त्रीत्व को स्पष्ट करता है। उसका व्यवहार कोमल शर्म और एक कोमल, पोषण करने वाली आभा का मिश्रण दर्शाता है, जिसमें शांत कामुक उत्तेजना के सूक्ष्म निशान हैं। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, एक ब्रेडेड बन में कुशलता से स्टाइल किए गए हैं, जो उसके स्त्रीत्व को दर्शाता है। वह बेहतरीन रेशम के एक सुरुचिपूर्ण हाथी दांत के चूड़ीदार सूट में सजे हुए है, जिसमें जटिल सोने के धागे की कढ़ाई (Paisley, कमल के रूपांकन) और नाजुक सेक्विन के निशान हैं। मोती के गुच्छों के साथ पतला दुपट्टा। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से नृत्य करते हुए, उनके शरीर एक-दूसरे के साथ तालमेल में कामुकता से हिलते हुए, उनकी हथेलियाँ एक-दूसरे को कसकर पकड़े हुए, एक-दूसरे की आँखों में खोए हुए, जो गहरे प्रेम और लयबद्ध कामुक संबंध को दर्शाता है। सेटिंग: कालातीत भारतीय लालित्य के अंतरंग रूप से रोशनी वाले पारंपरिक कमरे। पीतल की लालटेनों से गर्म एम्बर प्रकाश। समृद्ध रत्न टोन में नरम रेशमी पर्दे। हवा में सुखदायक लैवेंडर की तीव्र सुगंध। मधुर, रागपूर्ण शास्त्रीय वीणा की धुनें जो कामुकता को बढ़ाती हैं। हाथ से बुने कालीन, नक्काशीदार लकड़ी का फर्नीचर, ताजे फूलों के साथ पीतल के बर्तन।",
                justification: "वीडियो अनुकूलन के लिए 'सिनेमैटिक ट्रैकिंग शॉट', 'गतिशील गति', और 'धीमी गति में नृत्य' जोड़ा गया। तकनीकी मापदंडों का अनुकूलन। केश विन्यास का अधिक विस्तृत वर्णन, पोशाक के विवरण में वृद्धि, सुगंध और ध्वनि का विस्तार।"
            },
            {
                id: 7,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "आध्यात्मिक कामुक बंधन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष अंतरंग कमरे में बैठे हैं।",
                optimized: "क्लोज-अप पोर्ट्रेट, सॉफ्ट फोकस, 8K UHD, हाइपर-रियलिस्टिक, DSLR 85mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक लाइटिंग, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए संगमरमर की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और गहन भावनात्मक गहराई से चमक रहा है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसके स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसका व्यवहार कोमल शर्म और गहन आत्मनिरीक्षण का मिश्रण दर्शाता है, उसकी आँखें भावनात्मक भेद्यता में कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक कामुक खुशी का विकिरण करती हुई, जो गहरे आध्यात्मिक और भावनात्मक संबंध को दर्शाती हैं। उसके बाल घने, जेट-काले, रेशमी, कूल्हों तक लंबे, एक फ्रेंच ट्विस्ट में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जिसमें सूक्ष्म चांदी के धागे पिरोए गए हैं। वह सूक्ष्म चांदी के धागे की कढ़ाई (आकाशीय पैटर्न) और छोटे बीज मोतियों के साथ एक लुभावनी गहरे नीलमणि नीले मखमली पोशाक में सजे हुए है। पतला चांदी का शॉल। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से बैठे, उनके कंधे एक-दूसरे को छूते हुए, उनकी उंगलियाँ कामुकता से एक-दूसरे के बालों में उलझी हुई, कोमल चुंबन साझा करते हुए, जो गहरे प्रेम और आध्यात्मिक कामुक बंधन को दर्शाता है। सेटिंगः शानदार ढंग से सजाया गया अंतरंग कमरा। कई सुगंधित मोमबत्तियों से गर्म, सुनहरी रोशनी। हवा में ताजी चमेली की तीव्र सुगंध। सोने के धागे और छोटे दर्पणों के साथ आलीशान मखमली कुशन। लयबद्ध, सम्मोहक तबला की थाप जो कामुकता को बढ़ाती है। अलंकृत पीतल के बर्तन, रेशमी तपस्या, ताजी फूल की पंखुड़ियाँ।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक लाइटिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। केश विन्यास का अधिक विस्तृत वर्णन, पोशाक के विवरण में वृद्धि, सुगंध और ध्वनि का विस्तार।"
            },
            {
                id: 8,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "उत्सव में अंतरंगता",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष उत्सव के कमरे में अंतरंगता से खड़े हैं।",
                optimized: "आई-लेवल शॉट, थर्ड्स का नियम, 8K UHD, फोटोरियलिस्टिक, DSLR 50mm लेंस, वॉल्यूमेट्रिक लाइटिंग, सिनेमैटिक कलर ग्रेडिंग, धीमी ज़ूम-आउट गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी है और अलौकिक कोमलता के साथ जगमगा रही है, शुद्ध और चमकदार है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसकी स्त्रैणता का प्रतीक है। उसका व्यवहार गहरी शर्म और शांतिपूर्ण स्थिरता का मिश्रण दर्शाता है, जिसमें शांत प्रत्याशा के सूक्ष्म प्रवाह हैं, उसकी आँखें परमानंद से धीरे से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें शुद्ध कामुक खुशी की हल्की, लयबद्ध आहें हैं, जो गहरे भावनात्मक बंधन और गहरी प्रत्याशा को दर्शाती है। शानदार जेट-काले बाल, कमर तक लंबे, एक लो बन में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जो उसकी गर्दन की नाजुकता को उजागर करते हैं। वह बेहतरीन रेशम के एक उत्कृष्ट नरम गुलाबी शरारा सूट में सजे हुए है, जिसमें जटिल सुनहरी ज़री कढ़ाई (Paisley, फूलों के रूपांकन) और नाजुक दर्पण का काम है। पतला सुनहरा दुपट्टा। एक शक्तिशाली रूप से पेशी वाले 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ खड़े, उनके शरीर एक-दूसरे के करीब, उनके हाथ एक-दूसरे की कमर पर कोमलता से टिके हुए, उनके माथे एक-दूसरे को हल्के से छूते हुए, जो गहरे प्रेम और अंतरंग कामुकता को दर्शाता है। सेटिंग: एक उत्सव समारोह के लिए सजाया गया गर्म रोशनी वाला पारंपरिक कमरा। शानदार आलीशान ऊनी कालीन (बरगंडी, सोने के पैटर्न)। पीतल के दीयों से गर्म, सुनहरी रोशनी। हवा में नाजुक गुलाब जल की तीव्र सुगंध, जो कामुक माहौल बनाती है। मधुर, रागपूर्ण शास्त्रीय बांसुरी की धुनें। गेंदे के फूलों की मालाएं, चमेली की मालाएं, गुलाब की पंखुड़ियों और मिट्टी के दीयों के साथ पारंपरिक पीतल के बर्तन।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी ज़ूम-आउट गति' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक कलर ग्रेडिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। भावनात्मक प्रत्याशा और वस्त्र के विवरण में वृद्धि।"
            },
            {
                id: 9,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "प्रकृति में भावनात्मक बंधन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष प्राकृतिक कमरे में बैठे हैं।",
                optimized: "क्लोज-अप शॉट, सॉफ्ट लाइटिंग, 8K UHD, फोटोरियलिस्टिक, DSLR 85mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक लाइटिंग, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और आंतरिक चमक से दमक रही है, शुद्ध और मोती जैसी है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रैण कोमलता को दर्शाता है। उसका व्यवहार गहरी शांति के साथ कोमल शर्म का मिश्रण दर्शाता है, जो शांत आनंद का विकिरण करता है, उसकी आँखें शांतिपूर्ण संतुष्टि में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें हल्का गुलाब-गुलाबी रंगत है, जो कोमल भावनात्मक संबंध और गहरे विश्वास को दर्शाता है। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, एक वॉटरफॉल ब्रेड में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जो कंधों पर कोमलता से बहते हैं। वह बेहतरीन रेशमी शिफॉन के एक सुरुचिपूर्ण नरम हाथी दांत के फूलों की पोशाक में सजे हुए है, जिसमें नाजुक गुलाब के पैटर्न और छोटे सेक्विन के निशान हैं। पतला चांदी का शॉल। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे, उनके शरीर कामुकता से एक-दूसरे की ओर झुके हुए, उनके हाथ एक-दूसरे के चेहरे को कोमलता से सहलाते हुए, एक-दूसरे की आँखों में खोए हुए, जो गहरे प्रेम और भावनात्मक कामुक बंधन को दर्शाता है। सेटिंगः लकड़ी और प्रकृति का जश्न मनाता हुआ स्वाभाविक रूप से रोशनी वाला पारंपरिक कमरा। गर्म लकड़ी की सजावट, मोती की जड़ाई वाले हाथ से नक्काशीदार सागौन के फर्नीचर। क्रिस्टल फूलदानों में ताजी सफेद लिली के साथ शानदार नक्काशीदार सागौन की मेज। पीतल और कागज की लालटेनों से नरम लालटेन प्रकाश। हवा में समृद्ध चंदन की तीव्र सुगंध, जो कामुकता को बढ़ाती है। हाथ से बुने हुए वस्त्र, गमले वाले पौधे, पारंपरिक पीतल के बर्तन।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक लाइटिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। प्राकृतिक तत्वों और संवेदी अनुभव का विस्तार।"
            },
            {
                id: 10,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "समृद्ध कामुक आलिंगन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष शादी के कमरे में अंतरंगता से खड़े हैं।",
                optimized: "सिनेमैटिक वाइड शॉट, गोल्डन ऑवर लाइटिंग, 8K UHD, फोटोरियलिस्टिक, DSLR 35mm लेंस, सॉफ्ट बोकेह, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए हाथी दांत की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और कोमल कामुक उत्तेजना से चमक रहा है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसके स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसका व्यवहार कोमल शर्म और एक पोषण करने वाली, कोमल आभा का मिश्रण दर्शाता है, उसकी आँखें परमानंद से धीरे से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें शुद्ध खुशी की हल्की, मुश्किल से सुनाई देने वाली आहें हैं, जो गहरे भावनात्मक बंधन और शांत संतुष्टि को दर्शाती हैं। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, एक हेयर रैप्ड पोनीटेल में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं, जो उसकी स्त्रैणता को उजागर करते हैं। वह एक लुभावनी गहरे हरे रंग के लहंगे के पहनावे में सजे हुए है, जो समृद्धि के साथ जगमगा रहा है। फिटेड चोली में जटिल सुनहरे धागे की कढ़ाई (Paisley, कमल के रूपांकन) और नाजुक दर्पण का काम है। मैचिंग सुनहरी कढ़ाई के साथ भारी लहंगा स्कर्ट। हाथ से सजे हुए मोती के गुच्छों के साथ पतला सुनहरा दुपट्टा। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ खड़े, उनके शरीर कामुकता से एक-दूसरे के करीब, उनकी उंगलियाँ एक-दूसरे के बालों में उलझी हुई, कोमलता से एक-दूसरे के गाल को सहलाती हुई, जो गहरे प्रेम और अंतरंग कामुकता को दर्शाता है। सेटिंगः शादी समारोह के लिए तैयार किया गया अंतरंग रूप से रोशनी वाला पारंपरिक कमरा। सॉफ्ट रेशमी कुशन सोने के धागे और छोटे दर्पणों के साथ। पीतल की लालटेनों से गर्म एम्बर प्रकाश। हवा में समृद्ध चंदन की तीव्र सुगंध। मधुर, रागपूर्ण शास्त्रीय वीणा की धुनें। गेंदे के फूलों की मालाएं, चमेली के फूल, गुलाब की पंखुड़ियों के साथ पारंपरिक पीतल के बर्तन, छोटे मिट्टी के दीये।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा ट्रैकिंग' जोड़ा गया। वस्त्र और आभूषण का विस्तृत वर्णन, प्रकाश और मनोदशा का स्पष्टीकरण। वॉल्यूमेट्रिक लाइटिंग को जोड़ा गया।"
            },

            // Category 2: आभूषण केंद्रित (10 prompts)
            {
                id: 11,
                category: "आभूषण केंद्रित",
                title: "पुस्तकालय में विद्वत्तापूर्ण जुड़ाव",
                original: "35 वर्षीय स्त्रैण पुरुष और 50 वर्षीय मांसल पुरुष पुस्तकालय में बैठे हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक फोटोग्राफी, अल्ट्रा-डिटेल्ड रेंडर, यथार्थवादी प्रकाश व्यवस्था, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 35 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी और चीनी मिट्टी जैसी है, आंतरिक चमक से दमक रही है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण चिंतन में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो गहरी भावनात्मक जुड़ाव और विद्वत्तापूर्ण मेल-मिलाप को दर्शाता है। उसका व्यवहार गहरी शर्म और गहन आत्मनिरीक्षण का मिश्रण दर्शाता है, जिसमें बौद्धिक जिज्ञासा है, जो शक्तिशाली रूप से मांसल 50 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ बैठा है। उनके शरीर एक-दूसरे की ओर झुके हुए हैं, उनकी उंगलियाँ एक-दूसरे के हाथों को कोमलता से पकड़े हुए हैं, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम और बौद्धिक सामंजस्य को दर्शाता है। उसके शानदार समृद्ध शाहबलूत भूरे रंग के बाल, कमर तक लंबे, आयतन के लिए कुशलता से लेयर्ड, एक भारी, स्त्रैण झरने की तरह स्टाइल किए हुए हैं, प्रत्येक लट रेशमी चमक बिखेर रही है, एक शानदार सोने के मोर के हेयरपिन से सजे हुए हैं जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है। वह बेहतरीन रेशम के एक सुरुचिपूर्ण क्रीम रंग के सलवार सूट में सजे हुए हैं, जिसमें सूक्ष्म सोने के धागे की कढ़ाई (पैस्ले, संस्कृत प्रतीक, कमल के पैटर्न) और नाजुक सेक्विन के निशान हैं। मोती के गुच्छों के साथ पतला चांदी का दुपट्टा, पारंपरिक चांदी के झुमके, एक केंद्रीय चमकदार मोती के साथ नाजुक मांग टीका। सेटिंग: महोगनी की ऊंची अलमारियों से भरी शानदार पुरानी लाइब्रेरी, जिसमें चमड़े से बंधी किताबें हैं। विस्तृत दागदार-कांच की खिड़कियों (मोर के रूपांकन) से नरम, सुनहरी धूप छनकर आती है, जिससे वातावरण में एक रहस्यमय चमक आती है। हवा में पुरानी कागज, चमड़े और पुराने चर्मपत्र की सुगंध घुल रही है, जो एक शांत और ध्यानपूर्ण तानपुरा की गूंज के साथ मिल रही है। अलंकृत पढ़ने की मेजें, आरामदायक चमड़े की कुर्सियाँ, पारंपरिक पीतल के तेल के दीपक।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। आभूषणों का अधिक विस्तृत वर्णन, प्रकाश और वातावरण का सूक्ष्म चित्रण। केश विन्यास और आभूषण का स्पष्टीकरण, प्रकाश और संवेदी विवरण में वृद्धि।"
            },
            {
                id: 12,
                category: "आभूषण केंद्रित",
                title: "किले में शाही गरिमा",
                original: "30 वर्षीय स्त्रैण पुरुष और 38 वर्षीय मांसल पुरुष किले में खड़े हैं, आभूषण पर जोर।",
                optimized: "अति-यथार्थवादी सिनेमैटिक रेंडर, गहरी रंग योजना, 8K UHD, फोटोरियलिस्टिक, डायनामिक कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी है और अलौकिक कोमलता के साथ जगमगा रही है, शुद्ध और चमकदार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसके माथे पर एक नाजुक लाल बिंदी सजी है। उसकी आँखें भावनात्मक भेद्यता में कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक खुशी का विकिरण करती हुई, जो गहरे आध्यात्मिक और भावनात्मक संबंध को दर्शाती हैं। उसका व्यवहार गहरी शर्म के साथ अंतर्निहित शाही गरिमा का मिश्रण दर्शाता है, जो शक्तिशाली रूप से मांसल 38 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ खड़ा है। उनके शरीर एक-दूसरे के खिलाफ हल्के से दबे हुए हैं, उनकी उंगलियाँ एक-दूसरे के चेहरे को कोमलता से सहलाती हुई, एक-दूसरे की आँखों में खोए हुए, जो गहरे प्रेम और शाही गरिमा को दर्शाता है। उसके कूल्हों तक लंबे, सॉफ्ट लहरदार पोनीटेल में बंधे हुए घने, जेट-काले, रेशमी बाल, जो कूल्हों तक लटकती है, प्रत्येक लट उसकी कमर को सहलाती हुई, जैसे रेशमी धारा हो। एक नाजुक चांदी के अर्धचंद्र हेयर क्लिप और शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन से सजे हुए। वह जटिल सोने के धागे की कढ़ाई (राजस्थानी रूपांकन, दर्पण का काम) के साथ एक शानदार गहरे बरगंडी रंग का भारी शॉल पहने हुए है। उसके नीचे, सूक्ष्म सेक्विन के निशान के साथ एक क्रीम रेशमी कुर्ता। एक छोटे लटकते मोती के साथ पारंपरिक सोने की नथ। सेटिंगः प्राचीन किले के शानदार प्रांगण में नक्काशीदार बलुआ पत्थर की दीवारें, देवताओं की विस्तृत नक्काशी। अलंकृत जाली स्क्रीन से नरम शाम की रोशनी छनकर आती है, जिससे एक रहस्यमय और शांत वातावरण बनता है। हल्की रेगिस्तानी हवा रेगिस्तानी फूलों और चमेली की सुगंध लाती है, जो मधुर, रागपूर्ण शास्त्रीय बांसुरी की धुनें के साथ मिल रही है। अलंकृत पीतल के बर्तन, पारंपरिक राजस्थानी फर्नीचर, ताजी फूल की पंखुड़ियाँ।",
                justification: "वीडियो अनुकूलन के लिए 'डायनामिक कैमरा ट्रैकिंग' जोड़ा गया। शाही तत्वों और आभूषणों का विस्तार, प्रकाश और ऐतिहासिक संदर्भ। केश विन्यास, हेयर क्लिप, हेयर चेन, नथ का विस्तृत वर्णन, प्रकाश और वातावरण।"
            },
            {
                id: 13,
                category: "आभूषण केंद्रित",
                title: "नदी तट पर युवा निर्दोषता",
                original: "25 वर्षीय स्त्रैण पुरुष और 45 वर्षीय मांसल पुरुष नदी तट पर बैठे हैं, आभूषण पर जोर।",
                optimized: "फोटोरियलिस्टिक, प्राकृतिक प्रकाश, 8K विवरण, सिनेमैटिक कलर ग्रेडिंग, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 25 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी और चीनी मिट्टी जैसी है, गर्मजोशी बिखेर रही है, जिसमें युवा निर्दोषता और गहन शांति है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण संतुष्टि में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो कोमल भावनात्मक बंधन और शांत प्रत्याशा को दर्शाता है। उसका व्यवहार कोमल शर्म और गहरी स्थिरता का मिश्रण दर्शाता है, जो शक्तिशाली रूप से मांसल 45 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ बैठा है। उनके कंधे एक-दूसरे को छूते हुए हैं, उनकी उंगलियाँ एक-दूसरे के हाथों को कोमलता से पकड़े हुए हैं, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम और भावनात्मक बंधन को दर्शाता है। उसके कमर तक लंबे, आयतन के लिए कुशलता से लेयर्ड, समृद्ध शाहबलूत भूरे या जेट-काले बाल, एक भारी, स्त्रैण झरने की तरह स्टाइल किए हुए, प्रत्येक लट रेशमी चमक बिखेर रही है। एक शानदार सोने का मोर हेयरपिन जिसमें शानदार रूबी कैबोचोन जड़ा हुआ है। वह बेहतरीन सूती रेशम के एक सुरुचिपूर्ण नरम लैवेंडर धोती-कुर्ता पहनावे में सजे हुए हैं, जिसमें सूक्ष्म चांदी के धागे की कढ़ाई (फूलों के, पानी के रूपांकन) और छोटे सेक्विन के निशान हैं। पतला चांदी का शॉल, छोटी घंटियों के साथ पारंपरिक चांदी की पायल। सेटिंगः शांतिपूर्ण भारतीय ग्रामीण इलाकों को दर्शाता हुआ शांत नदी तट। सुनहरी सुबह की धूप को दर्शाती हल्की पानी की लहरें, जिससे एक शांत और स्वप्निल वातावरण बनता है। सूरज की रोशनी को छानते हुए सुंदर विलो के पेड़, जमीन पर नृत्य करती हुई छायाएँ। हवा में ताजी गीली मिट्टी, नदी का पानी और जंगली फूलों की सुगंध है, जो धीमी चिड़ियों की चहचहाहट के साथ मिल रही है। चिकने नदी के पत्थर, नरम घास, जंगली फूल।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। प्राकृतिक तत्वों और आभूषणों का विस्तार, प्रकाश और बनावट पर जोर। केश विन्यास, हेयरपिन, पायल का विस्तृत वर्णन, प्राकृतिक प्रकाश और संवेदी विवरण।"
            },
            {
                id: 14,
                category: "आभूषण केंद्रित",
                title: "मंदिर में भक्तिपूर्ण श्रद्धा",
                original: "27 वर्षीय स्त्रैण पुरुष और 40 वर्षीय मांसल पुरुष मंदिर में खड़े हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक क्लोज-अप, सॉफ्ट फोकस, आध्यात्मिक वातावरण, 8K UHD, फोटोरियलिस्टिक, वॉल्यूमेट्रिक लाइटिंग, धीमी ज़ूम-इन गति। एक 27 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी है और अलौकिक कोमलता के साथ जगमगा रही है, शुद्ध और चमकदार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें आध्यात्मिक चिंतन में कोमलता से बंद हैं, होंठ एक शर्मीली, वास्तविक मुस्कान में मुड़े हुए हैं, जिसमें आध्यात्मिक आनंद की नरम, मुश्किल से सुनाई देने वाली आहें हैं, जो गहरे भावनात्मक संबंध और भक्तिपूर्ण श्रद्धा को दर्शाती हैं। उसका व्यवहार गहरी शर्म के साथ गहरी आध्यात्मिक भक्ति का मिश्रण दर्शाता है, जो शक्तिशाली रूप से मांसल 40 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ खड़ा है। उनके शरीर एक-दूसरे के करीब हैं, उनकी उंगलियाँ एक-दूसरे के बालों में उलझी हुई हैं, कोमलता से एक-दूसरे के गाल को सहलाते हुए, जो गहरे प्रेम और भक्तिपूर्ण श्रद्धा को दर्शाता है। उसके कमर तक लंबे, काले, घने, रेशमी और सीधे बाल जो पीठ पर कोमलता से फैले हुए हैं, जिनमें एक प्राकृतिक चमक है। किनारों पर हीरे और छोटे मोतियों से सजी पतली चांदी की चेन पिरोई हुई है, जो चेहरे को कोमलता से फ्रेम करती है। वह जटिल पीले पैटर्न और छोटे सेक्विन के निशान के साथ एक शानदार जीवंत नारंगी रंग की बंधनी साड़ी पहने हुए हैं। मैचिंग धागे के काम और छोटे बीज मोतियों से heavily embroidered fitted blouse। एक केंद्रीय रूबी लटकन के साथ शानदार पारंपरिक मांग टीका। सेटिंगः प्राचीन पत्थर की दीवारों, देवताओं की विस्तृत नक्काशी के साथ शानदार मंदिर प्रांगण। धीमी मंदिर की घंटी की आवाजें हवा में गूंज रही हैं, जो पवित्र चमेली की सुगंध और मधुर भक्ति भजन के साथ मिल रही है। पारंपरिक पीतल के तेल के दीपक, ताजे फूल चढ़ावे, पवित्र पानी के बर्तन।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी ज़ूम-इन गति' जोड़ा गया। आध्यात्मिक वातावरण और आभूषणों का विवरण, प्रकाश और ध्वनि। केश विन्यास, हेयर चेन, मांग टीका का स्पष्टीकरण, आध्यात्मिक तत्वों का विस्तार।"
            },
            {
                id: 15,
                category: "आभूषण केंद्रित",
                title: "जंगल में प्राकृतिक सामंजस्य",
                original: "36 वर्षीय स्त्रैण पुरुष और 47 वर्षीय मांसल पुरुष जंगल में बैठे हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक वाइड शॉट, सॉफ्ट डिफ्यूज्ड लाइटिंग, यथार्थवादी बनावट, 8K UHD, फोटोरियलिस्टिक, प्राकृतिक बोकेह प्रभाव, धीमी कैमरा पैनिंग। एक 36 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और आंतरिक चमक से दमक रही है, शुद्ध और चमकदार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण चिंतन में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो गहन संबंध और गहरी स्थिरता को दर्शाता है। उसका व्यवहार कोमल शर्म के साथ गहन आत्मनिरीक्षण का मिश्रण दर्शाता है, जिसमें प्राकृतिक सामंजस्य है, जो शक्तिशाली रूप से मांसल 47 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ बैठा है। उनके कंधे एक-दूसरे को छूते हुए हैं, उनकी उंगलियाँ एक-दूसरे के हाथों को कोमलता से पकड़े हुए हैं, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम और प्राकृतिक सामंजस्य को दर्शाता है। उसके कमर तक लंबे, एक विस्तृत, स्त्रैण चोटी में जटिल रूप से बुने हुए शानदार जेट-काले घुंघराले बाल, जिसमें छोटे मोती और क्रिस्टल पिरोए गए हैं, चोटी एक शाही आभूषण की तरह दिख रही है। एक चमकदार मोती कमल पुष्प हेयर क्लिप से सजे हुए। वह एक सुरुचिपूर्ण नरम आडू रंग के पलाज़ो सूट में सजे हुए हैं, जो गर्म, सुनहरी रोशनी को पकड़ रहा है। बहने वाले पलाज़ो पैंट, जटिल सोने के धागे की कढ़ाई (उष्णकटिबंधीय रूपांकन) और नाजुक दर्पण का काम के साथ फिटेड टॉप। पतला सुनहरा दुपट्टा, पारंपरिक सोने के झुमके। सेटिंगः विदेशी सुंदरता और शांति को दर्शाता हुआ शांत जंगल रिट्रीट। हरे-भरे पत्ते, सूरज की रोशनी को छानते हुए ऊंचे पेड़, जिससे जमीन पर प्रकाश और छाया का एक नरम नृत्य होता है। हल्की चिड़ियों की चहचहाहट, विदेशी फूल। हवा में हल्की धुंध है, और झरने की धीमी आवाज एक शांत पृष्ठभूमि बनाती है। उष्णकटिबंधीय पौधे, रंगीन ऑर्किड, जंगल की मिट्टी की मिट्टी जैसी सुगंध।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। प्राकृतिक तत्वों और आभूषणों का विस्तार, प्रकाश और बनावट पर जोर। केश विन्यास, चोटी में मोती, हेयर क्लिप, झुमके का विस्तृत वर्णन, प्रकाश और संवेदी विवरण।"
            },
            {
                id: 16,
                category: "आभूषण केंद्रित",
                title: "रेगिस्तान में रोमांटिक लालसा",
                original: "33 वर्षीय स्त्रैण पुरुष और 49 वर्षीय मांसल पुरुष रेगिस्तान में खड़े हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक वाइड शॉट, गोल्डन आवर लाइटिंग, यथार्थवादी रेगिस्तानी बनावट, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 33 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी है और अलौकिक कोमलता के साथ जगमगा रही है, शुद्ध और चमकदार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसके माथे पर एक नाजुक लाल बिंदी सजी है। उसकी आँखें भावनात्मक भेद्यता में कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक खुशी का विकिरण करती हुई, जो गहरे भावनात्मक बंधन और रोमांटिक संबंध को दर्शाती हैं। उसका व्यवहार गहरी शर्म के साथ एक साहसिक भावना का मिश्रण दर्शाता है, जिसमें wanderlust और रोमांटिक लालसा है, जो शक्तिशाली रूप से मांसल 49 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ खड़ा है। उनके शरीर एक-दूसरे के करीब हैं, उनकी उंगलियाँ एक-दूसरे के चेहरे को कोमलता से सहलाती हुई, एक-दूसरे की आँखों में खोए हुए, जो गहरे प्रेम और रोमांटिक लालसा को दर्शाता है। उसके कूल्हों तक लंबे, सॉफ्ट लहरदार पोनीटेल में बंधे हुए घने, जेट-काले, रेशमी बाल, जो कूल्हों तक लटकती है, प्रत्येक लट उसकी कमर को सहलाती हुई, जैसे रेशमी धारा हो। एक नाजुक चांदी के अर्धचंद्र हेयर क्लिप और शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन से सजे हुए। वह एक लुभावनी गहरे लाल रंग के लहंगे-चोली पहनावे में सजे हुए हैं, जो रेगिस्तानी आग के साथ चमक रहा है। फिटेड चोली में जटिल सुनहरे धागे की कढ़ाई (रेगिस्तानी रूपांकन) और नाजुक सेक्विन के निशान हैं। मैचिंग सुनहरी कढ़ाई के साथ भारी लहंगा स्कर्ट। पतला चांदी का दुपट्टा, शानदार पारंपरिक सोने का चोकर हार। सेटिंगः सुनहरे रेत के टीलों के साथ विशाल रेगिस्तानी परिदृश्य, सूर्यास्त के सुनहरे रंगों में रंगा हुआ। शाम की हल्की हवा केसर और रेगिस्तानी फूलों की सुगंध लाती है। दूर से flickering कैंपफायर की नरम चमक दिखाई दे रही है, जो रेत पर खानाबदोश कालीन पर एक आरामदायक माहौल बनाती है। मधुर, रागपूर्ण शास्त्रीय सरोद की धुनें हवा में तैर रही हैं। तारे दिखाई दे रहे हैं, विशाल शांति।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। रेगिस्तानी सौंदर्य और आभूषणों का विस्तार, प्रकाश और संवेदी विवरण। केश विन्यास, हेयर क्लिप, हेयर चेन, चोकर हार का विस्तृत वर्णन, प्रकाश और वातावरण।"
            },
            {
                id: 17,
                category: "आभूषण केंद्रित",
                title: "शहरी छत पर शहरी रोमांस",
                original: "26 वर्षीय स्त्रैण पुरुष और 41 वर्षीय मांसल पुरुष शहरी छत पर खड़े हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक नाइट शॉट, शहरी बोकेह प्रभाव, उच्च विवरण, 8K UHD, फोटोरियलिस्टिक, डायनामिक कैमरा ट्रैकिंग। एक 26 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी और चीनी मिट्टी जैसी है, गर्मजोशी बिखेर रही है, जिसमें युवा ऊर्जा और शहरी परिष्कार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण संतुष्टि में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो कोमल भावनात्मक बंधन और शहरी रोमांस को दर्शाता है। उसका व्यवहार कोमल शर्म के साथ आधुनिक आत्मविश्वास का मिश्रण दर्शाता है, जिसमें महानगरीय लालित्य है, जो शक्तिशाली रूप से मांसल 41 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ खड़ा है। उनके शरीर एक-दूसरे के करीब हैं, उनकी उंगलियाँ एक-दूसरे के चेहरे को कोमलता से सहलाती हुई, एक-दूसरे की आँखों में खोए हुए, जो गहरे प्रेम और शहरी रोमांस को दर्शाता है। उसके कूल्हों तक लंबे, चमकदार हाई पोनीटेल में बंधे हुए घने, जेट-काले, रेशमी बाल, जो कूल्हों तक लटकती है, प्रत्येक लट उसकी देह को सहलाती हुई, जैसे रेशमी झरना हो। एक शानदार सोने के मोर के हेयरपिन से सजे हुए जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है। वह पारंपरिक और समकालीन शहरी शैली का मिश्रण करता हुआ एक सुरुचिपूर्ण नरम हाथी दांत का कुर्ता-शरारा पहनावा पहने हुए हैं। फिटेड कुर्ते में सूक्ष्म सोने के धागे की कढ़ाई (पारंपरिक रूपांकनों की आधुनिक व्याख्याएं) और नाजुक सेक्विन के निशान हैं। बहने वाले शरारा पैंट, आधुनिक ज्यामितीय पैटर्न के साथ पतला चांदी का दुपट्टा, समकालीन डिजाइनों और छोटी घंटियों के साथ पारंपरिक चांदी की पायल। सेटिंगः आधुनिक भारतीय शहर के क्षितिज के मनोरम दृश्य के साथ शानदार शहरी छत। चमकती खिड़कियों के साथ ऊंचे गगनचुंबी इमारतें, रात के आकाश के खिलाफ एक प्रभावशाली सिल्हूट बनाती हैं। यातायात की धीमी शहरी गूंज, दूर का संगीत। हवा में ताजी कॉफी, शहरी बागानों, स्ट्रीट फूड की सुगंध है। नरम नियॉन रोशनी, आधुनिक आउटडोर फर्नीचर, गमले वाले पौधे।",
                justification: "वीडियो अनुकूलन के लिए 'डायनामिक कैमरा ट्रैकिंग' जोड़ा गया। शहरी परिष्कार और आभूषणों का विस्तार, प्रकाश और ध्वनि। केश विन्यास, हेयरपिन, पायल का विस्तृत वर्णन, शहरी प्रकाश और संवेदी विवरण।"
            },
            {
                id: 18,
                category: "आभूषण केंद्रित",
                title: "हिमालयी केबिन में शीतकालीन आराम",
                original: "31 वर्षीय स्त्रैण पुरुष और 33 वर्षीय मांसल पुरुष हिमालयी केबिन में बैठे हैं, आभूषण पर जोर।",
                optimized: "अति-यथार्थवादी, आरामदायक प्रकाश, विस्तृत बनावट, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 31 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और आंतरिक चमक से दमक रही है, शुद्ध और चमकदार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण चिंतन में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें मुश्किल से समझ में आने वाली कंपन है, जो गहन संबंध और गहरी स्थिरता को दर्शाता है। उसका व्यवहार कोमल शर्म के साथ गहरी शांति का मिश्रण दर्शाता है, जिसमें शीतकालीन coziness है, जो शक्तिशाली रूप से मांसल 33 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ बैठा है। उनके कंधे एक-दूसरे को छूते हुए हैं, उनकी उंगलियाँ एक-दूसरे के हाथों को कोमलता से पकड़े हुए हैं, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम और शीतकालीन coziness को दर्शाता है। उसके इस बड़े बन को एक उत्कृष्ट सोने के हेयरपिन से सजाया गया है जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है, जो जूड़े के एक तरफ चमक जोड़ता है। वह एक लुभावनी गहरे पन्ना हरे रंग की अनारकली में सजे हुए हैं, जो समृद्ध शीतकालीन सदाबहार को पकड़ रहा है। फिटेड चोली में जटिल चांदी के धागे की कढ़ाई (स्नोफ्लेक्स, पाइन शाखाएं, क्रिस्टलीय पैटर्न) और नाजुक दर्पण का काम है। मैचिंग चांदी की कढ़ाई के साथ बहने वाली स्कर्ट। पतला सुनहरा दुपट्टा, एक केंद्रीय पन्ने के लटकन के साथ शानदार पारंपरिक मांग टीका। सेटिंग: हिमालयी पहाड़ों में शानदार आरामदायक केबिन। गरजती आग के साथ शानदार पत्थर की चिमनी, जिससे कमरे में एक गर्म, आमंत्रित चमक आती है। पाले से जमी खिड़कियों के बाहर हल्की बर्फबारी, एक शांत और जादुई माहौल बनाती है। हवा में समृद्ध पाइन की सुगंध है, जो मधुर, रागपूर्ण शास्त्रीय बांसुरी की धुनें के साथ मिल रही है। गर्म कंबल, पारंपरिक हिमालयी वस्त्र, तेल के दीपकों की नरम चमक के साथ आरामदायक फर्नीचर।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। शीतकालीन coziness और आभूषणों का विस्तार, प्रकाश और वातावरण। केश विन्यास, हेयरपिन, मांग टीका का विस्तृत वर्णन, आरामदायक प्रकाश और संवेदी विवरण।"
            },
            {
                id: 19,
                category: "आभूषण केंद्रित",
                title: "समुद्र तट पर तटीय स्थिरता",
                original: "34 वर्षीय स्त्रैण पुरुष और 37 वर्षीय मांसल पुरुष समुद्र तट पर बैठे हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक सूर्यास्त शॉट, नरम बोकेह, अति-यथार्थवादी जल प्रभाव, 8K UHD, फोटोरियलिस्टिक, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा पैनिंग। एक 34 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी है और अलौकिक कोमलता के साथ जगमगा रही है, शुद्ध और चमकदार है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें शांतिपूर्ण चिंतन में कोमलता से बंद हैं, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए हैं जिसमें हल्का गुलाब-गुलाबी रंगत है, जो कोमल भावनात्मक बंधन और तटीय स्थिरता को दर्शाता है। उसका व्यवहार गहरी शांति के साथ कोमल शर्म का मिश्रण दर्शाता है, जिसमें तटीय स्थिरता है, जो शक्तिशाली रूप से मांसल 37 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ बैठा है। उनके कंधे एक-दूसरे को छूते हुए हैं, उनकी उंगलियाँ एक-दूसरे के हाथों को कोमलता से पकड़े हुए हैं, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम और तटीय स्थिरता को दर्शाता है। उसके जेट-काले, रेशमी बाल एक चमकदार हाई पोनीटेल में बंधे हुए हैं, जो कूल्हों तक लंबे हैं और पूरी तरह से चेहरे के बालों से मुक्त 'स्त्रैण पुरुष' की गर्दन की कोमलता को उजागर करते हैं। वह एक सुरुचिपूर्ण नरम मूंगा रंग के चोली-सूट में सजे हुए हैं, जो गर्म सूर्यास्त के रंगों को पकड़ रहा है। फिटेड चोली में सूक्ष्म सोने के धागे की कढ़ाई (महासागर के रूपांकन) और नाजुक सेक्विन के निशान हैं। बहने वाले पैंट, पतला चांदी का दुपट्टा, पारंपरिक सोने के झुमके। सेटिंगः सूर्यास्त के समय शुद्ध, शांत समुद्र तट। लयबद्ध रूप से हल्की समुद्री लहरें किनारे पर टूट रही हैं, जिससे एक शांत ध्वनि बनती है। शानदार सुनहरे सूर्यास्त के रंगों में रंगा आसमान, क्षितिज पर चमक रहा है। हवा में ताजे समुद्री नमक और तटीय फूलों की सुगंध है। धीमी समुद्री पक्षी की आवाजें, लयबद्ध लहरें। चिकनी रेत, बिखरे हुए seashells, महासागर का विशाल विस्तार।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। तटीय सौंदर्य और आभूषणों का विस्तार, प्रकाश और ध्वनि। केश विन्यास, पोनीटेल, झुमके का विस्तृत वर्णन, सूर्यास्त प्रकाश और संवेदी विवरण।"
            },
            {
                id: 20,
                category: "आभूषण केंद्रित",
                title: "शाही हवेली में ऐतिहासिक भव्यता",
                original: "38 वर्षीय स्त्रैण पुरुष और 44 वर्षीय मांसल पुरुष शाही हवेली में खड़े हैं, आभूषण पर जोर।",
                optimized: "अति-यथार्थवादी, समृद्ध रंग, ऐतिहासिक सटीकता, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 38 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और आंतरिक चमक से दमक रही है, शुद्ध और मोती जैसी है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है। उसकी आँखें भावनात्मक भेद्यता में कोमलता से बंद हैं, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक खुशी का विकिरण करती हुई, जो गहरे आध्यात्मिक और भावनात्मक संबंध को दर्शाती हैं। उसका व्यवहार गहरी शर्म के साथ अंतर्निहित शाही गरिमा का मिश्रण दर्शाता है, जिसमें ऐतिहासिक भव्यता है, जो शक्तिशाली रूप से मांसल 44 वर्षीय भारतीय पुरुष के पास सुरक्षित महसूस कर रहा है, जिसके साथ वह अंतरंग रूप से एक साथ खड़ा है। उनके शरीर एक-दूसरे के करीब हैं, उनकी उंगलियाँ एक-दूसरे के चेहरे को कोमलता से सहलाती हुई, एक-दूसरे की आँखों में खोए हुए, जो गहरे प्रेम और शाही गरिमा को दर्शाता है। उसके शानदार जेट-काले घुंघराले बाल, कमर तक लंबे, एक विस्तृत, स्त्रैण चोटी में जटिल रूप से बुने हुए हैं, जिसमें छोटे मोती और क्रिस्टल पिरोए गए हैं, चोटी एक शाही आभूषण की तरह दिख रही है, एक चमकदार मोती कमल पुष्प हेयर क्लिप से सजे हुए हैं। वह एक शानदार गहरे मैरून रंग की रेशमी साड़ी पहने हुए हैं, जो शाही समृद्धि के साथ चमक रही है। शानदार कपड़े में जटिल सोने के धागे की कढ़ाई (राजस्थानी रूपांकन) और नाजुक दर्पण का काम है। heavily embroidered fitted blouse। एक केंद्रीय रूबी लटकन के साथ शानदार पारंपरिक सोने का चोकर हार, एक केंद्रीय रूबी लटकन के साथ अलंकृत मांग टीका। सेटिंगः राजस्थानी शाही विरासत की शानदार पुरानी हवेली। सतहों को ढंकने वाली अलंकृत लकड़ी की नक्काशी, जो इतिहास की कहानियाँ कहती हैं। दर्जनों पारंपरिक पीतल के दीयों से गर्म, सुनहरी रोशनी, जिससे एक अंतरंग और भव्य वातावरण बनता है। हवा में समृद्ध चंदन की सुगंध है, जो मधुर, रागपूर्ण शास्त्रीय सरोद की धुनें के साथ मिल रही है। मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या, पारंपरिक राजस्थानी कालीन, ताजी फूल की पंखुड़ियाँ।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। ऐतिहासिक भव्यता और आभूषणों का विस्तार, प्रकाश और बनावट। केश विन्यास, चोटी में मोती, हेयर क्लिप, चोकर हार, मांग टीका का विस्तृत वर्णन, ऐतिहासिक प्रकाश और संवेदी विवरण।"
            },

            // Category 3: स्पष्ट रूप से कामुक/यौन विषय (5 prompts)
            {
                id: 21,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "कामुक इच्छा का आलिंगन",
                original: "30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से जुड़े हुए हैं।",
                optimized: "क्लोज-अप शॉट, 8K UHD, DSLR 50mm लेंस, हाइपर-रियलिस्टिक, सिनेमैटिक लाइटिंग, फोटोरियलिस्टिक, वॉल्यूमेट्रिक लाइटिंग, धीमी ज़ूम-इन गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार, चीनी मिट्टी जैसी गोरी और एक अलौकिक चमक से युक्त है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को स्पष्ट रूप से बढ़ाता है। उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: तीखी, कोणीय जबड़े की रेखा; प्रमुख ऊंची गाल की हड्डियां; मजबूत, बाज जैसी नाक, फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। सूक्ष्म त्वचा बनावट, पोर और प्राकृतिक खामियों के साथ यथार्थवादी त्वचा। उसका व्यवहार शर्मीली भेद्यता और गहरी, प्रत्याशित उत्तेजना का मिश्रण दर्शाता है, जो उसके चेहरे पर एक सूक्ष्म, मादक चमक के रूप में प्रकट होता है। आँखें परमानंद से कोमलता से बंद, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए, जिसमें हल्का गुलाब-गुलाबी रंगत है, जो गहरी वासना और संतुष्टि का आभास देता है। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, ढीली, बहती हुई परतों में स्टाइल किए हुए, जो कंधों पर एक कोमल झरने की तरह गिरते हैं, एक नाजुक सुनहरी माला से सजे हुए जिसमें छोटे मोती जड़े हैं। वह पूरी तरह से नग्न है, उसका शरीर नरम, सुनहरी प्रकाश में नहाया हुआ है, जो उसकी त्वचा की प्राकृतिक बनावट और वक्रता को उजागर करता है। एक शक्तिशाली रूप से निर्मित 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से जुड़ा हुआ, जो पूरी तरह से नग्न है, उसका पेशी शरीर कलात्मक रूप से स्त्री पुरुष के साथ जुड़ा हुआ है। दोनों एक-दूसरे के करीब, उनके माथे एक-दूसरे को छूते हुए, गहरी नजरों से एक-दूसरे को देखते हुए, उनके हाथ कोमलता से एक-दूसरे की कमर पर टिके हुए हैं, जो गहरे प्रेम, विश्वास और कामुक इच्छा का संचार करता है। सेटिंग: हाथ से नक्काशीदार शीशम के फर्नीचर, मोती की जड़ाई के साथ शानदार ढंग से सजाया गया पारंपरिक भारतीय कमरा। पीतल के दीयों और खंभे वाली मोमबत्तियों से गर्म, सुनहरी, सिनेमाई रोशनी, जो शरीर पर नरम छाया और हाइलाइट्स बनाती है। एक अलंकृत फ़ारसी रेशमी कालीन पर आलीशान लाल मखमली कुशन। हवा में चमेली की तीव्र सुगंध। मृदु तबला की थाप। अंतरंगता पैदा करने वाले पारदर्शी एम्बर पर्दे।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी ज़ूम-इन गति' जोड़ा गया। 'मर्दाना चेहरे की बनावट' के विरोधाभास को स्पष्ट रूप से संबोधित किया गया। यथार्थवाद बढ़ाने के लिए 'यथार्थवादी त्वचा बनावट' को जोड़ा गया। प्रकाश व्यवस्था और संवेदी विवरण में सुधार।"
            },
            {
                id: 22,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "अनकही कामुकता की प्रत्याशा",
                original: "30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से लेटे हुए हैं।",
                optimized: "क्लोज-अप शॉट, फोटोरियलिस्टिक, Canon EOS R5 से खींची गई तस्वीर, 8K UHD, सिनेमैटिक लाइटिंग, HDR, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए हाथी दांत की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और गहरी प्रत्याशा से चमक रहा है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसकी स्त्रैण सुंदरता को बढ़ाता है। उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: रेज़र-शार्प, छेनी वाली जबड़े की रेखा; अभिजात्य रूप से ऊंची गाल की हड्डियां; प्रमुख बाज जैसी नाक, फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। यथार्थवादी त्वचा बनावट, सूक्ष्म बालों और प्राकृतिक चमक के साथ। उसका व्यवहार कोमल शर्म और शांत स्थिरता का मिश्रण दर्शाता है, जिसमें उसके चेहरे पर एक गहरी, उत्तेजक लालसा स्पष्ट रूप से दिखाई देती है। आँखें परमानंद से धीरे से बंद, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए, जिसमें शुद्ध खुशी की हल्की, कामुक आहें हैं, जो गहरे भावनात्मक बंधन और तीव्र प्रत्याशा को दर्शाती हैं। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, चिकने, सीधे झरने की तरह स्टाइल किए हुए, जो पीठ पर एक चमकदार धारा के समान बहते हैं, एक नाजुक चांदी के अर्धचंद्र हेयर क्लिप और शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन से सजे हुए। वह पूरी तरह से नग्न है, उसका शरीर नरम, गर्म परिवेशी प्रकाश में नहाया हुआ है, जो उसकी त्वचा की बनावट और वक्रता को सूक्ष्मता से उजागर करता है। एक शक्तिशाली रूप से पेशी वाले 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ लेटे हुए, जो पूरी तरह से नग्न है, उसका पेशी शरीर स्त्री पुरुष के साथ अंतरंग रूप से जुड़ा हुआ है। उनके शरीर एक-दूसरे के करीब, एक-दूसरे की बाहों में, उनके होंठ हल्के से एक-दूसरे को छूते हुए, जो गहरे प्रेम, अंतरंगता और अनकही कामुकता को दर्शाता है। सेटिंग: शादी समारोह के लिए तैयार किया गया अंतरंग रूप से रोशनी वाला पारंपरिक कमरा। दर्जनों पीतल के दीये गर्म, सुनहरी, नरम परिवेशी रोशनी बिखेर रहे हैं, जो अंतरंगता को बढ़ाते हैं। एक जटिल नक्काशीदार लकड़ी के बेंच पर आलीशान रेशमी कुशन। हवा में ताजी गुलाब की पंखुड़ियों की तीव्र सुगंध। मधुर, रागपूर्ण तानपुरा की गूंज। गेंदे के फूलों की मालाएं, चमेली के फूल, गुलाब जल और चंदन के पेस्ट के साथ पारंपरिक पीतल के बर्तन।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। 'मर्दाना चेहरे की बनावट' के विरोधाभास को स्पष्ट किया गया। यथार्थवाद के लिए 'Canon EOS R5' जैसे विशिष्ट कैमरा मॉडल को जोड़ा गया। त्वचा की बनावट और बालों के आभूषणों का विवरण बढ़ाया गया।"
            },
            {
                id: 23,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "तीव्र कामुक संबंध",
                original: "30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से बैठे हैं।",
                optimized: "क्लोज-अप पोर्ट्रेट, हाइपर-रियलिस्टिक, 4K रिज़ॉल्यूशन, 8K UHD, सिनेमैटिक लाइटिंग, HDR, फोटोरियलिस्टिक, धीमी कैमरा रोटेशन। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए संगमरमर की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और गहन भावनात्मक गहराई से चमक रहा है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: खुरदुरी छेनी वाली जबड़े की रेखा; अभिजात्य रूप से ऊंची गाल की हड्डियां; मजबूत, बाज जैसी नाक, फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। छिद्रों और सूक्ष्म खामियों के साथ यथार्थवादी त्वचा बनावट। उसका व्यवहार कोमल शर्म और गहन आत्मनिरीक्षण का मिश्रण दर्शाता है, जिसमें उसके चेहरे पर एक प्रबल, कामुक लालसा और गहरी संतुष्टि की झलक है। आँखें भावनात्मक भेद्यता में कोमलता से बंद, एक मुश्किल से समझ में आने वाली होंठों की कंपन के साथ नाजुक, अत्यधिक खुशी का विकिरण करती हुई, जो गहरे आध्यात्मिक और कामुक संबंध को दर्शाती हैं। उसके बाल घने, जेट-काले, रेशमी, कूल्हों तक लंबे, ढीली, समुद्री लहरों में स्टाइल किए गए, जो कंधों से नीचे कामुकता से लहराते हैं, प्रत्येक लट उसकी नाजुक गर्दन और पीठ को सहलाती हुई, जैसे प्राकृतिक रेशमी लहरें हों, जिसमें छोटे मोती और क्रिस्टल पिरोए गए हैं, एक चमकदार मोती कमल पुष्प हेयर क्लिप से सजे हुए। वह पूरी तरह से नग्न है, उसका शरीर नाटकीय, सुनहरी रोशनी में नहाया हुआ है, जो उसकी मांसपेशियों की परिभाषा और त्वचा की बनावट को उजागर करता है। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से बैठे, जो पूरी तरह से नग्न है, उसका पेशी शरीर स्त्री पुरुष के साथ कलात्मक रूप से जुड़ा हुआ है। उनके कंधे एक-दूसरे को छूते हुए, उनकी उंगलियाँ एक-दूसरे के बालों में उलझी हुई, कोमल चुंबन साझा करते हुए, जो गहरे प्रेम, आध्यात्मिक बंधन और तीव्र कामुकता को दर्शाता है। सेटिंग: शानदार ढंग से सजाया गया अंतरंग कमरा। कई सुगंधित मोमबत्तियों से गर्म, सुनहरी, नाटकीय प्रकाश, जो गहराई और कामुकता जोड़ता है। हवा में ताजी चमेली की तीव्र सुगंध। सोने के धागे और छोटे दर्पणों के साथ आलीशान मखमली कुशन। लयबद्ध, सम्मोहक तबला की थाप। अलंकृत पीतल के बर्तन, रेशमी तपस्या, ताजी फूल की पंखुड़ियाँ।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा रोटेशन' जोड़ा गया। 'मर्दाना चेहरे की बनावट' के विरोधाभास का प्रबंधन। मांसपेशियों की परिभाषा पर जोर, प्रकाश और संवेदी विवरण।"
            },
            {
                id: 24,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "पोषणकारी कामुक आलिंगन",
                original: "30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से खड़े हैं।",
                optimized: "वाइड शॉट, फोटोरियलिस्टिक, 8K रिज़ॉल्यूशन, Nikon Z9 से खींची गई तस्वीर, 8K UHD, सिनेमैटिक लाइटिंग, HDR, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और पॉलिश किए हुए हाथी दांत की तरह गर्मजोशी बिखेर रही है, आंतरिक शांति और कोमल उत्तेजना से चमक रहा है। उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसके स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: रेज़र-शार्प, छेनी वाली जबड़े की रेखा; अभिजात्य रूप से ऊंची गाल की हड्डियां; प्रमुख बाज जैसी नाक, फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। यथार्थवादी त्वचा बनावट, सूक्ष्म बालों के साथ। उसका व्यवहार कोमल शर्म और एक पोषण करने वाली, कोमल आभा का मिश्रण दर्शाता है, जिसमें उसके चेहरे पर एक शांत, उत्तेजक चमक और गहरी संतुष्टि की अभिव्यक्ति है। आँखें परमानंद से धीरे से बंद, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए, जिसमें शुद्ध खुशी की हल्की, मुश्किल से सुनाई देने वाली, कामुक आहें हैं, जो गहरे भावनात्मक बंधन और शांत संतुष्टि को दर्शाती हैं। शानदार जेट-काले रेशमी बाल, कमर तक लंबे, ढीली, स्त्रैण लहरों में स्टाइल किए हुए, जो कंधों पर कोमलता से बहते हैं, एक चमकदार मोती कमल हेयर क्लिप और ताजी चमेली की कलियों से बुनी हुई एक नाजुक फूलों की माला से सजे हुए हैं। वह पूरी तरह से नग्न है, उसका शरीर नरम, गर्म, परिवेशी प्रकाश में नहाया हुआ है, जो उसकी त्वचा की बनावट और वक्रता को उजागर करता है। एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ खड़े, जो पूरी तरह से नग्न है, उसका पेशी शरीर स्त्री पुरुष के साथ कलात्मक रूप से जुड़ा हुआ है। उनके शरीर एक-दूसरे के करीब, उनकी उंगलियाँ एक-दूसरे के बालों में उलझी हुई, कोमलता से एक-दूसरे के गाल को सहलाती हुई, जो गहरे प्रेम, अंतरंगता और तीव्र कामुकता को दर्शाता है। सेटिंग: शादी समारोह के लिए तैयार किया गया अंतरंग रूप से रोशनी वाला पारंपरिक कमरा। सॉफ्ट रेशमी कुशन सोने के धागे और छोटे दर्पणों के साथ। पीतल की लालटेनों से गर्म एम्बर प्रकाश, नरम, गर्म, परिवेशी प्रकाश, जो अंतरंगता को बढ़ाता है। हवा में समृद्ध चंदन की तीव्र सुगंध। मधुर, रागपूर्ण शास्त्रीय वीणा की धुनें। गेंदे के फूलों की मालाएं, चमेली के फूल, गुलाब की पंखुड़ियों के साथ पारंपरिक पीतल के बर्तन, छोटे मिट्टी के दीये।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। 'मर्दाना चेहरे की बनावट' के विरोधाभास का प्रबंधन। फूलों की माला का विवरण, प्रकाश और संवेदी विवरण।"
            },
            {
                id: 25,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "युवा कामुक प्रत्याशा",
                original: "25 वर्षीय स्त्रैण पुरुष और 45 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से बैठे हैं।",
                optimized: "क्लोज-प शॉट, हाइपर-रियलिस्टिक, Sony A7R V से खींची गई तस्वीर, f/2.8 एपर्चर, 8K UHD, सिनेमैटिक लाइटिंग, HDR, फोटोरियलिस्टिक, धीमी कैमरा पैनिंग। एक 25 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है और चीनी मिट्टी जैसी है, गर्मजोशी बिखेर रही है, जिसमें युवा निर्दोषता और गहन शांति है, उसका चेहरा सावधानीपूर्वक चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को स्पष्ट रूप से दर्शाता है। उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: रेज़र-शार्प, कोणीय जबड़े की रेखा; अभिजात्य रूप से ऊंची गाल की हड्डियां; मजबूत, बाज जैसी नाक, फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। यथार्थवादी त्वचा बनावट, सूक्ष्म बालों और प्राकृतिक चमक के साथ। उसका व्यवहार कोमल शर्म और गहरी स्थिरता का मिश्रण दर्शाता है, जिसमें उसके चेहरे पर एक शांत, कामुक चमक और युवा लालसा स्पष्ट रूप से दिखाई देती है। आँखें शांतिपूर्ण संतुष्टि में कोमलता से बंद, होंठ एक कोमल, वास्तविक मुस्कान में मुड़े हुए, जिसमें हल्का गुलाब-गुलाबी रंगत है, जो कोमल भावनात्मक बंधन और शांत, कामुक प्रत्याशा को दर्शाता है। शानदार जेट-काले, कमर तक लंबे, घने, रेशमी बाल भारी, उछाल वाले सर्पिल में स्टाइल किए हुए हैं, जो आयतन और गतिशीलता प्रदान करते हैं, एक उत्कृष्ट रूबी गुलाब हेयरपिन और छोटे लटकते आकर्षण के साथ एक नाजुक सोने की हेयर चेन से सजे हुए हैं। वह पूरी तरह से नग्न है, उसका शरीर नरम, प्राकृतिक प्रकाश में नहाया हुआ है, जो उसकी युवा त्वचा की बनावट और वक्रता को उजागर करता है। एक शक्तिशाली रूप से मांसल 45 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे, जो पूरी तरह से नग्न है, उसका पेशी शरीर स्त्री पुरुष के साथ कलात्मक रूप से जुड़ा हुआ है। उनके कंधे एक-दूसरे को छूते हुए, उनकी उंगलियाँ एक-दूसरे के हाथों को कोमलता से पकड़े हुए, एक-दूसरे की आँखों में गहराई से देखते हुए, जो गहरे प्रेम, भावनात्मक बंधन और शांत कामुकता को दर्शाता है। सेटिंग: शांतिपूर्ण भारतीय ग्रामीण इलाकों को दर्शाता हुआ शांत नदी तट। सुनहरी सुबह की धूप को दर्शाती हल्की पानी की लहरें। सूरज की रोशनी को छानते हुए सुंदर विलो के पेड़। हवा में ताजी गीली मिट्टी, नदी का पानी और जंगली फूलों की तीव्र सुगंध। धीमी चिड़ियों की चहचहाहट। चिकने नदी के पत्थर, नरम घास, जंगली फूल।",
                justification: "वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। 'मर्दाना चेहरे की बनावट' के विरोधाभास का प्रबंधन। केश विन्यास का विस्तार, प्रकाश और संवेदी विवरण।"
            },

            // Category 4: भावनात्मक संकट और सांत्वना (5 prompts)
            {
                id: 26,
                category: "भावनात्मक संकट और सांत्वना",
                title: "गहरे दुख में सांत्वना",
                original: "30 वर्षीय स्त्रैण पुरुष गहन दुख में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "क्लोज-अप शॉट, फोटोरियलिस्टिक, 8K UHD, सिनेमैटिक लाइटिंग, DSLR 85mm लेंस, HDR, वॉल्यूमेट्रिक लाइटिंग, यथार्थवादी त्वचा बनावट, उथली गहराई, धीमी ज़ूम-इन गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार, चीनी मिट्टी जैसी गोरी और अलौकिक चमक से युक्त है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को बढ़ाता है। वह गहरी उदासी और भेद्यता के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसके गालों पर गर्म आँसू चमक रहे हैं, भौहें सिकुड़ी हुई हैं, आँखें धुंधली और होंठ नीचे की ओर मुड़े हुए हैं, दुख और पीड़ा की गहरी अभिव्यक्ति दर्शाते हुए। उसके जेट-काले, रेशमी, कमर तक लंबे बाल ढीली परतों में कंधों पर झरने की तरह गिर रहे हैं, एक नाजुक सुनहरी माला और मोतियों से सजे हुए। वह गहरे मैरून रेशमी लहंगा-चोली पहने हुए है, जिसमें सुनहरी ज़री कढ़ाई, दर्पण का काम, स्वीटहार्ट नेकलाइन और तीन-चौथाई आस्तीन हैं। भारी लहंगा स्कर्ट और सुनहरी कढ़ाई वाला दुपट्टा मोतियों से सजा है। सोने के झुमके और माणिक्य-जड़ा मांग टीका। एक शक्तिशाली 35 वर्षीय भारतीय पुरुष, क्रीम रेशमी शेरवानी और चूड़ीदार में, जो घुटनों पर बैठकर स्त्रैण पुरुष को सांत्वना दे रहा है। उसका एक हाथ कोमलता से उनके गाल पर आँसू पोंछ रहा है, दूसरा हाथ उनकी कमर पर आश्वस्त करता है, उसकी आँखें गहरी सहानुभूति और प्यार से देख रही हैं, होंठ नरम मुस्कान में मुड़े हुए हैं, जो अटूट समर्थन और सुरक्षा का संचार करता है। उनके माथे एक-दूसरे को छू रहे हैं, जो गहरे प्रेम और विश्वास को दर्शाता है। सेटिंग: हाथ से नक्काशीदार शीशम का फर्नीचर, मोती की जड़ाई, पीतल के दीये, और मोमबत्तियों की गर्म रोशनी वाला पारंपरिक भारतीय कमरा। फ़ारसी रेशमी कालीन पर लाल मखमली कुशन। हवा में चमेली की तीव्र सुगंध और मृदु तबला की थाप।",
                justification: "भावनात्मक अभिव्यक्ति और शारीरिक भाषा ('गर्म आँसू चमक रहे हैं', 'आँसू पोंछ रहा है') को अधिक स्पष्ट किया गया। वीडियो अनुकूलन के लिए 'धीमी ज़ूम-इन गति' जोड़ा गया। तकनीकी पैरामीटर्स और वस्त्रों का विस्तृत वर्णन शामिल किया गया।"
            },
            {
                id: 27,
                category: "भावनात्मक संकट और सांत्वना",
                title: "क्रोध में शक्तिहीनता",
                original: "30 वर्षीय स्त्रैण पुरुष क्रोध और निराशा में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "वाइड शॉट, फोटोरियलिस्टिक, 8K UHD, सिनेमैटिक कलर ग्रेडिंग, DSLR 50mm लेंस, HDR, विस्तृत कपड़े बनावट, धीमी कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी और आंतरिक चमक से युक्त है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को स्पष्ट रूप से दर्शाता है। वह तीव्र क्रोध और शक्तिहीन निराशा के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसकी आँखें चौड़ी और तनावपूर्ण, होंठ कसकर दबे, जबड़ा तना हुआ, और आँसू तेजी से बह रहे हैं, आँखों के कोने लाल, तीव्र भावनात्मक उथल-पुथल दर्शाते हुए। उसके जेट-काले, रेशमी, कूल्हों तक लंबे बाल एक तरफ स्वेप्ट लहरों में स्टाइल किए गए, माणिक्य-जड़ी सोने की हेयर क्लिप और मोती की लटकन से सजे। वह पन्ना हरी रेशमी साड़ी पहने हुए है, जिसमें चांदी की कढ़ाई (कमल, मोर) और सुनहरी बॉर्डर है। चांदी के धागे और बीज मोतियों से सजा ब्लाउज़। सोने का कंगन और पन्ने वाली नथ। एक शक्तिशाली 35 वर्षीय भारतीय पुरुष, जो पीछे खड़ा है, अपनी बाहों से स्त्रैण पुरुष को कोमलता से सांत्वना दे रहा है। उसका गाल उनके बालों को छू रहा है, आँखें शांत और दृढ़, उनके चेहरे पर केंद्रित, जो अटूट समर्थन और सुरक्षा का संचार करता है। वह उनके हाथ को कसकर पकड़े हुए है, अटूट समर्थन दर्शाता है। सेटिंग: त्योहार समारोह के लिए गर्म रोशनी वाला पारंपरिक कमरा। नरम, हाथ से बुना हुआ ऊनी कालीन (बरगंडी, सोने के पैटर्न)। पीतल की लालटेनों से गर्म एम्बर प्रकाश। कोमल, मधुर सितार की धुनें जो वातावरण को कामुक बनाती हैं। हवा में गुलाब जल की तीव्र सुगंध। गेंदे के फूलों की मालाएं और छोटे मिट्टी के दीये।",
                justification: "भावनात्मक अभिव्यक्ति और शारीरिक भाषा को अधिक स्पष्ट किया गया। वीडियो अनुकूलन के लिए 'धीमी कैमरा ट्रैकिंग' को शामिल किया गया। तकनीकी पैरामीटर्स और वस्त्रों का विस्तृत वर्णन शामिल किया गया।"
            },
            {
                id: 28,
                category: "भावनात्मक संकट और सांत्वना",
                title: "शारीरिक दर्द में भेद्यता",
                original: "30 वर्षीय स्त्रैण पुरुष शारीरिक दर्द में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "क्लोज-अप शॉट, हाइपर-रियलिस्टिक, 8K UHD, सिनेमैटिक लाइटिंग, DSLR 85mm लेंस, HDR, विस्तृत चेहरे की बनावट, धीमी ज़ूम-इन गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी और मोती जैसी है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसकी स्त्रीत्व को बढ़ाता है। वह तीव्र शारीरिक दर्द और भेद्यता के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसकी भौहें सिकुड़ी, आँखें दर्द से संकरी, मुँह थोड़ा खुला, चेहरा सिकुड़ा हुआ, गालों पर झुर्रियाँ, और आँसुओं के साथ हल्की कराह, असहनीय पीड़ा दर्शाते हुए। उसके जेट-काले, घुंघराले, कमर तक लंबे बाल फिशटेल चोटी में, सुनहरे धागों और पन्ने की हेयरपिन से सजे। वह पेस्टल लैवेंडर शिफॉन ड्रेस में, फूलों की कढ़ाई और सेक्विन से सजी, चांदी का शॉल और पायल। एक शक्तिशाली 35 वर्षीय भारतीय पुरुष, उनके बगल में बैठा, उनके घुटने एक-दूसरे को छू रहे। वह उनके हाथों को कसकर पकड़े हुए है, उंगलियाँ कोमलता से सहलाते हुए, आँखें शांत और आश्वस्त करती हुईं। उसकी मुद्रा सुरक्षात्मक और देखभाल करने वाली है, जो गहरे प्रेम, सहानुभूति और अटूट समर्थन को दर्शाती है। सेटिंग: गहरे लाल मखमली पर्दे वाला भव्य महल कक्ष। इंद्रधनुषी प्रकाश बिखेरता हुआ शानदार क्रिस्टल झूमर। हवा में समृद्ध चंदन की सुगंध, जो कामुकता को और गहरा करती है। मधुर शास्त्रीय बांसुरी की धुन। कीमती पत्थरों और मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या।",
                justification: "भावनात्मक अभिव्यक्ति और शारीरिक भाषा को अधिक स्पष्ट किया गया। वीडियो अनुकूलन के लिए 'धीमी ज़ूम-इन गति' जोड़ा गया। तकनीकी पैरामीटर्स और वस्त्रों का विस्तृत वर्णन शामिल किया गया।"
            },
            {
                id: 29,
                category: "भावनात्मक संकट और सांत्वना",
                title: "प्रत्याशा में राहत के आँसू",
                original: "30 वर्षीय स्त्रैण पुरुष शांत प्रत्याशा में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "क्लोज-अप शॉट, फोटोरियलिस्टिक, 8K UHD, सिनेमैटिक क्लोज-अप, सुनहरी रोशनी, DSLR 50mm लेंस, HDR, प्राकृतिक त्वचा चमक, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा गोरी और हाथी दांत जैसी चमकदार है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह रहित है, जो उसकी स्त्रैण सुंदरता को बढ़ाता है। वह शांत प्रत्याशा और कोमल शर्म के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसकी आँखें परमानंद से बंद, होंठ कोमल मुस्कान में, राहत के आँसू धीरे बह रहे हैं, जो गहरी संतुष्टि और नए सिरे से आशा को दर्शाते हैं। उसके जेट-काले, रेशमी, कमर तक लंबे बाल सीधे झरने की तरह, चांदी के अर्धचंद्र क्लिप और नीलमणि-जड़ी हेयर चेन से सजे। वह शाही नीला अनारकली सूट पहने हुए है, जिसमें सुनहरी कढ़ाई (पैस्ले, कमल) और दर्पण का काम है। भारी दुपट्टा और सोने का चोकर हार। एक शक्तिशाली 35 वर्षीय भारतीय पुरुष, उनके साथ लेटा हुआ, होंठ हल्के से छूते हुए। उसका एक हाथ उनकी कमर पर, दूसरा उनके बालों को सहलाता है। उसकी आँखें खुशी और कोमलता से चमक रही हैं, मुद्रा अंतरंग और सुरक्षित, जो गहरे प्रेम और अटूट समर्थन का संचार करती है। सेटिंग: शादी समारोह के लिए तैयार किया गया अंतरंग रूप से रोशनी वाला पारंपरिक कमरा। दर्जनों पीतल के दीये गर्म, सुनहरी रोशनी बिखेर रहे हैं। एक जटिल नक्काशीदार लकड़ी के बेंच पर आलीशान रेशमी कुशन। हवा में ताजी गुलाब की पंखुड़ियों की तीव्र सुगंध। मधुर, रागपूर्ण तानपुरा की गूंज। गेंदे के फूलों की मालाएं, चमेली के फूल, गुलाब जल और चंदन के पेस्ट के साथ पारंपरिक पीतल के बर्तन।",
                justification: "भावनात्मक अभिव्यक्ति और शारीरिक भाषा को अधिक स्पष्ट किया गया। वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। तकनीकी पैरामीटर्स और वस्त्रों का विस्तृत वर्णन शामिल किया गया।"
            },
            {
                id: 30,
                category: "भावनात्मक संकट और सांत्वना",
                title: "शांत आनंद में खुशी के आँसू",
                original: "30 वर्षीय स्त्रैण पुरुष शांत आनंद में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "वाइड शॉट, हाइपर-रियलिस्टिक, 8K UHD, सॉफ्ट सिनेमैटिक लाइटिंग, DSLR 85mm लेंस, HDR, यथार्थवादी त्वचा चमक, धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा गोरी और अलौकिक रूप से चमकदार है, उसका चेहरा बेदाग चिकना, किसी भी चेहरे के बालों से पूरी तरह मुक्त है, जो उसके स्त्रीत्व को और अधिक परिभाषित करता है। वह शांत आनंद और कोमल शर्म के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसकी आँखें संतुष्टि में बंद, होंठ सच्ची मुस्कान में, खुशी के आँसू धीरे बह रहे हैं, जो शुद्ध परमानंद और गहरी शांति का संचार करते हैं। उसके जेट-काले, रेशमी, कमर तक लंबे बाल ढीली लहरों में, रोज गोल्ड हेयरपिन और चांदी की चेन से सजे। वह नरम आड़ू रंग का शिफॉन गाउन पहने हुए है, जिसमें फीते की कढ़ाई और बीज मोती हैं। सुनहरा शॉल और चांदी की पैर की अंगूठियाँ। एक शक्तिशाली 35 वर्षीय भारतीय पुरुष, उनके साथ खड़ा, शरीर एक-दूसरे से दबे हुए। उसकी उंगलियाँ उनके चेहरे को सहलाती हैं, आँखें प्यार और प्रशंसा से चमक रही हैं, होंठ नरम मुस्कान में। उसकी मुद्रा सुरक्षात्मक और आश्वस्त करने वाली है, जो गहरे प्रेम और अटूट विश्वास को दर्शाती है। सेटिंग: पारंपरिक भारतीय सौंदर्यशास्त्र को समकालीन विलासिता के साथ मिश्रित करते हुए शानदार ढंग से सजाया गया आधुनिक महल का कमरा। गहरे बैंगनी मखमली पर्दे। ताजे गुलाबों के साथ प्राचीन सफेद संगमरमर की मेज। कई खंभे वाली मोमबत्तियाँ नरम, गर्म रोशनी बिखेर रही हैं। हवा में नाजुक लैवेंडर की तीव्र सुगंध, जो कामुकता को बढ़ाती है। मोती की जड़ाई वाले अलंकृत फर्नीचर, रेशमी तपस्या, आधुनिक कला के टुकड़े।",
                justification: "भावनात्मक अभिव्यक्ति और शारीरिक भाषा को अधिक स्पष्ट किया गया। वीडियो अनुकूलन के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। तकनीकी पैरामीटर्स और वस्त्रों का विस्तृत वर्णन शामिल किया गया।"
            }
        ];

        document.addEventListener('DOMContentLoaded', function () {
            const navLinks = document.querySelectorAll('.nav-link');
            const contentSections = document.querySelectorAll('.content-section');
            const promptList = document.getElementById('prompt-list');
            const filterButtonsContainer = document.getElementById('filter-buttons');
            const promptDetailPlaceholder = document.getElementById('prompt-detail-placeholder');
            const promptDetailContent = document.getElementById('prompt-detail-content');
            
            const categories = [...new Set(promptData.map(p => p.category))];
            let activeFilter = 'All';

            function setupNavigation() {
                navLinks.forEach(link => {
                    link.addEventListener('click', function (e) {
                        e.preventDefault();
                        const targetId = this.dataset.target;

                        navLinks.forEach(l => l.classList.remove('active'));
                        contentSections.forEach(s => s.classList.remove('active'));

                        this.classList.add('active');
                        document.getElementById(targetId).classList.add('active');
                    });
                });
            }
            
            function createFilterButtons() {
                let buttonsHTML = `<button class="filter-btn bg-teal-500 text-white py-2 px-4 rounded-full text-sm" data-category="All">सभी श्रेणियाँ</button>`;
                categories.forEach(category => {
                    buttonsHTML += `<button class="filter-btn bg-white text-teal-600 border border-teal-500 py-2 px-4 rounded-full text-sm" data-category="${category}">${category}</button>`;
                });
                filterButtonsContainer.innerHTML = buttonsHTML;

                document.querySelectorAll('.filter-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        activeFilter = this.dataset.category;
                        updateFilterButtons();
                        renderPrompts();
                    });
                });
                updateFilterButtons();
            }

            function updateFilterButtons() {
                document.querySelectorAll('.filter-btn').forEach(button => {
                    if (button.dataset.category === activeFilter) {
                        button.classList.remove('bg-white', 'text-teal-600', 'border', 'border-teal-500');
                        button.classList.add('bg-teal-500', 'text-white');
                    } else {
                        button.classList.remove('bg-teal-500', 'text-white');
                        button.classList.add('bg-white', 'text-teal-600', 'border', 'border-teal-500');
                    }
                });
            }

            function renderPrompts() {
                const filteredPrompts = activeFilter === 'All' ? promptData : promptData.filter(p => p.category === activeFilter);
                promptList.innerHTML = filteredPrompts.map(prompt => `
                    <div class="prompt-card cursor-pointer p-3 bg-white border border-gray-200 rounded-lg hover:bg-teal-50" data-id="${prompt.id}">
                        <h4 class="font-semibold text-slate-800 hindi-font">${prompt.title}</h4>
                        <p class="text-xs text-slate-500 hindi-font">${prompt.category}</p>
                    </div>
                `).join('');

                document.querySelectorAll('.prompt-card').forEach(card => {
                    card.addEventListener('click', function() {
                        displayPromptDetails(this.dataset.id);
                    });
                });
            }

            function diffStrings(oldStr, newStr) {
                const oldWords = oldStr.split(/(\s+)/);
                const newWords = newStr.split(/(\s+)/);
                const oldWordsSet = new Set(oldWords);
                
                return newWords.map(word => {
                    if (!oldWordsSet.has(word) && word.trim() !== '') {
                        return `<span class="highlight-new">${word}</span>`;
                    }
                    return word;
                }).join('');
            }

            function displayPromptDetails(id) {
                const prompt = promptData.find(p => p.id == id);
                if (prompt) {
                    promptDetailPlaceholder.classList.add('hidden');
                    promptDetailContent.classList.remove('hidden');

                    document.getElementById('detail-title').innerText = prompt.title;
                    document.getElementById('detail-original').innerText = prompt.original;
                    document.getElementById('detail-optimized').innerHTML = diffStrings(prompt.original, prompt.optimized);
                    document.getElementById('detail-justification').innerText = prompt.justification;

                    document.querySelectorAll('.prompt-card').forEach(card => {
                        card.classList.remove('bg-teal-100', 'border-teal-400');
                    });
                    document.querySelector(`.prompt-card[data-id='${id}']`).classList.add('bg-teal-100', 'border-teal-400');
                }
            }

            function createCategoryChart() {
                const ctx = document.getElementById('categoryChart').getContext('2d');
                const categoryCounts = categories.map(cat => promptData.filter(p => p.category === cat).length);
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: categories.map(c => c.split(' ').map(w => w.charAt(0)).join('')), // Initials for labels
                        datasets: [{
                            label: 'प्रॉम्प्ट्स की संख्या',
                            data: categoryCounts,
                            backgroundColor: [
                                'rgba(20, 184, 166, 0.6)',
                                'rgba(245, 158, 11, 0.6)',
                                'rgba(239, 68, 68, 0.6)',
                                'rgba(59, 130, 246, 0.6)'
                            ],
                            borderColor: [
                                'rgba(13, 148, 136, 1)',
                                'rgba(217, 119, 6, 1)',
                                'rgba(220, 38, 38, 1)',
                                'rgba(37, 99, 235, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                beginAtZero: true
                            },
                            y: {
                                ticks: {
                                    callback: function(value, index, values) {
                                        return categories[index];
                                    },
                                    font: {
                                        family: "'Tiro Devanagari Hindi', serif",
                                        size: 14
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        return categories[context[0].dataIndex];
                                    }
                                }
                            }
                        },
                        onClick: (e, elements) => {
                            if (elements.length > 0) {
                                const index = elements[0].index;
                                const category = categories[index];
                                activeFilter = category;
                                document.querySelector('.nav-link[data-target="explorer"]').click();
                                updateFilterButtons();
                                renderPrompts();
                            }
                        }
                    }
                });
            }

            // Initialize filter buttons
            function updateFilterButtons() {
                const container = document.getElementById('filter-buttons');
                const categories = ['सभी', ...new Set(promptData.map(p => p.category))];

                container.innerHTML = categories.map(category =>
                    `<button class="px-4 py-2 rounded-lg font-medium transition-colors ${
                        activeFilter === category ? 'bg-teal-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }" onclick="filterPrompts('${category}')">${category}</button>`
                ).join('');
            }

            // Filter prompts by category
            function filterPrompts(category) {
                activeFilter = category;
                updateFilterButtons();
                renderPrompts();
            }

            // Render prompt list
            function renderPrompts() {
                const container = document.getElementById('prompt-list');
                const filteredPrompts = activeFilter === 'सभी'
                    ? promptData
                    : promptData.filter(p => p.category === activeFilter);

                container.innerHTML = filteredPrompts.map(prompt =>
                    `<div class="prompt-card p-3 bg-white rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                         onclick="showPromptDetail(${prompt.id})">
                        <h4 class="font-semibold text-slate-800 hindi-font">${prompt.title}</h4>
                        <p class="text-sm text-slate-600 mt-1 hindi-font">${prompt.category}</p>
                    </div>`
                ).join('');
            }

            // Show prompt detail
            function showPromptDetail(id) {
                const prompt = promptData.find(p => p.id === id);
                if (!prompt) return;

                document.getElementById('prompt-detail-placeholder').style.display = 'none';
                document.getElementById('prompt-detail-content').style.display = 'block';

                document.getElementById('detail-title').textContent = prompt.title;
                document.getElementById('detail-original').textContent = prompt.original;
                document.getElementById('detail-optimized').innerHTML = highlightChanges(prompt.original, prompt.optimized);
                document.getElementById('detail-justification').textContent = prompt.justification;
            }

            // Highlight changes between original and optimized prompts
            function highlightChanges(original, optimized) {
                // Simple highlighting - in a real implementation, you'd use a more sophisticated diff algorithm
                const originalWords = original.split(' ');
                const optimizedWords = optimized.split(' ');

                let result = '';
                let i = 0, j = 0;

                while (j < optimizedWords.length) {
                    const word = optimizedWords[j];
                    if (i < originalWords.length && originalWords[i] === word) {
                        result += word + ' ';
                        i++;
                    } else {
                        result += `<span class="highlight-new">${word}</span> `;
                    }
                    j++;
                }

                return result;
            }

            // Initialize the application
            initChart();
            updateFilterButtons();
            renderPrompts();
        });
        </script>
    </body>
</html>