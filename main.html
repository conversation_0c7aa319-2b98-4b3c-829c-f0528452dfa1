<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>इंटरैक्टिव प्रॉम्प्ट ऑप्टिमाइज़ेशन रिपोर्ट</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Tiro+Devanagari+Hindi:wght@400&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Tiro Devanagari Hindi', sans-serif;
        }
        .hindi-font {
            font-family: 'Tiro Devanagari Hindi', serif;
        }
        .nav-link {
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .nav-link.active {
            border-bottom-color: #0d9488;
            color: #0d9488;
        }
        .prompt-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .prompt-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
        .highlight-new {
            background-color: #ccfbf1;
            padding: 1px 3px;
            border-radius: 4px;
            font-weight: 500;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-slate-800">

    <div id="app" class="container mx-auto p-4 md:p-8">
        <header class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-teal-700 hindi-font">एआई प्रॉम्प्ट ऑप्टिमाइज़ेशन रिपोर्ट</h1>
            <p class="mt-4 text-lg text-slate-600">एआई-जनित छवियों और वीडियो के लिए प्रॉम्प्ट को बेहतर बनाने के लिए एक इंटरैक्टिव गाइड।</p>
        </header>

        <nav class="flex justify-center border-b border-gray-200 mb-8">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500">
                <li class="mr-2">
                    <a href="#home" class="nav-link inline-block p-4 rounded-t-lg active" data-target="home">
                        <span class="hindi-font text-lg">मुख्य पृष्ठ</span>
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#guide" class="nav-link inline-block p-4 rounded-t-lg" data-target="guide">
                         <span class="hindi-font text-lg">सिद्धांत</span>
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#explorer" class="nav-link inline-block p-4 rounded-t-lg" data-target="explorer">
                         <span class="hindi-font text-lg">प्रॉम्प्ट एक्सप्लोरर</span>
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#learnings" class="nav-link inline-block p-4 rounded-t-lg" data-target="learnings">
                         <span class="hindi-font text-lg">मुख्य सीख</span>
                    </a>
                </li>
            </ul>
        </nav>

        <main>
            <!-- Home Section -->
            <section id="home" class="content-section active">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-4 hindi-font">रिपोर्ट का परिचय</h2>
                    <p class="text-slate-700 leading-relaxed hindi-font">
                        यह रिपोर्ट एआई-आधारित छवि और वीडियो जनरेशन के लिए उपयोगकर्ता द्वारा प्रदान किए गए प्रॉम्प्ट्स के गहन विश्लेषण, त्रुटि सुधार और अनुकूलन पर केंद्रित है। इसका प्राथमिक उद्देश्य इन प्रॉम्प्ट्स को "पूरी तरह से अनुकूलित" करना है, यह सुनिश्चित करते हुए कि वे एआई मॉडल्स से उच्च-गुणवत्ता वाले, सटीक और रचनात्मक आउटपुट उत्पन्न करें। यह इंटरैक्टिव एप्लिकेशन आपको रिपोर्ट के मुख्य निष्कर्षों को आसानी से नेविगेट करने और समझने में मदद करने के लिए डिज़ाइन किया गया है।
                    </p>
                    <div class="mt-8">
                        <h3 class="text-2xl font-bold text-teal-600 mb-4 hindi-font">प्रॉम्प्ट श्रेणियों का अवलोकन</h3>
                        <p class="text-slate-700 mb-4 hindi-font">रिपोर्ट में प्रॉम्प्ट्स को चार मुख्य श्रेणियों में बांटा गया है। नीचे दिया गया चार्ट प्रत्येक श्रेणी में प्रॉम्प्ट्स की संख्या को दर्शाता है। श्रेणियों का पता लगाने के लिए चार्ट पर क्लिक करें।</p>
                        <div class="chart-container bg-white p-4 rounded-lg shadow-inner">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Guide Section -->
            <section id="guide" class="content-section">
                 <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-6 hindi-font">प्रभावी प्रॉम्प्ट इंजीनियरिंग के सिद्धांत</h2>
                    <div class="space-y-4">
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">मुख्य घटक</h3>
                            <p class="text-slate-600 hindi-font">एक प्रभावी प्रॉम्प्ट में स्पष्ट घटक होने चाहिए: विषय, क्रिया, वातावरण, माध्यम/शैली, प्रकाश, रंग, मनोदशा और संरचना।</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">विशिष्टता का महत्व</h3>
                            <p class="text-slate-600 hindi-font">जितना अधिक विशिष्ट और विस्तृत प्रॉम्प्ट होगा, एआई मॉडल द्वारा वांछित परिणाम उत्पन्न करने की संभावना उतनी ही अधिक होगी। 'बड़ा' के बजाय 'विशाल' जैसे विशिष्ट पर्यायवाची शब्दों का प्रयोग करें।</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">तकनीकी पैरामीटर्स</h3>
                            <p class="text-slate-600 hindi-font">8K UHD, DSLR 85mm लेंस, सिनेमैटिक कलर ग्रेडिंग जैसे तकनीकी विवरण शामिल करने से गुणवत्ता और शैली में काफी सुधार होता है।</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">नकारात्मक प्रॉम्प्ट्स</h3>
                            <p class="text-slate-600 hindi-font">अवांछित तत्वों, बनावटों या विशेषताओं को बाहर करने के लिए `--neg` या इसी तरह के कमांड का उपयोग करें। यह आउटपुट को परिष्कृत करने में मदद करता है।</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Explorer Section -->
            <section id="explorer" class="content-section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-2 hindi-font">प्रॉम्प्ट एक्सप्लोरर</h2>
                    <p class="text-slate-600 mb-6 hindi-font">श्रेणी के अनुसार प्रॉम्प्ट्स को फ़िल्टर करें और प्रत्येक के मूल और अनुकूलित संस्करणों की तुलना करें। विवरण देखने के लिए किसी भी प्रॉम्प्ट शीर्षक पर क्लिक करें।</p>

                    <div id="filter-buttons" class="flex flex-wrap justify-center gap-2 mb-8">
                        <!-- Filter buttons will be generated here -->
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-12 gap-8">
                        <div id="prompt-list-container" class="md:col-span-4">
                            <h3 class="text-xl font-semibold mb-4 text-slate-700 hindi-font">प्रॉम्प्ट्स</h3>
                            <div id="prompt-list" class="space-y-2 max-h-[600px] overflow-y-auto pr-2">
                                <!-- Prompt list will be generated here -->
                            </div>
                        </div>
                        <div id="prompt-detail-container" class="md:col-span-8 bg-gray-50 p-6 rounded-lg">
                            <div id="prompt-detail-placeholder" class="text-center text-slate-500 flex items-center justify-center h-full">
                                <p class="hindi-font">तुलना देखने के लिए बाईं ओर से एक प्रॉम्प्ट चुनें।</p>
                            </div>
                            <div id="prompt-detail-content" class="hidden">
                                <h3 id="detail-title" class="text-2xl font-bold text-teal-700 mb-4 hindi-font"></h3>
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div>
                                        <h4 class="text-lg font-semibold mb-2 border-b-2 border-red-300 pb-1 hindi-font">मूल प्रॉम्प्ट</h4>
                                        <p id="detail-original" class="text-sm text-slate-600 leading-relaxed bg-red-50 p-3 rounded-md hindi-font"></p>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold mb-2 border-b-2 border-green-300 pb-1 hindi-font">अनुकूलित प्रॉम्प्ट</h4>
                                        <p id="detail-optimized" class="text-sm text-slate-700 leading-relaxed bg-green-50 p-3 rounded-md hindi-font"></p>
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <h4 class="text-lg font-semibold mb-2 border-b-2 border-blue-300 pb-1 hindi-font">परिवर्तनों का औचित्य</h4>
                                    <p id="detail-justification" class="text-sm text-slate-600 leading-relaxed bg-blue-50 p-3 rounded-md hindi-font"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Learnings Section -->
            <section id="learnings" class="content-section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-6 hindi-font">मुख्य सीख और सिफारिशें</h2>
                    <ul class="space-y-4 list-disc list-inside text-slate-700">
                        <li class="hindi-font"><span class="font-semibold">पुनरावृत्ति महत्वपूर्ण है:</span> वांछित परिणाम प्राप्त करने के लिए प्रॉम्प्ट्स को लगातार परिष्कृत करने की आवश्यकता होती है।</li>
                        <li class="hindi-font"><span class="font-semibold">संक्षिप्तता बनाम विवरण:</span> एआई को स्पष्ट दिशा देने के लिए पर्याप्त विवरण प्रदान करें, लेकिन अनावश्यक शब्दों से बचें जो फोकस को कमजोर कर सकते हैं।</li>
                        <li class="hindi-font"><span class="font-semibold">विशिष्ट शब्दावली:</span> सटीक पर्यायवाची और ठोस भाषा का उपयोग करने से एआई की व्याख्या क्षमता में काफी वृद्धि होती है।</li>
                        <li class="hindi-font"><span class="font-semibold">नकारात्मक प्रॉम्प्ट्स:</span> अवांछित तत्वों को फ़िल्टर करके आउटपुट को ठीक करने के लिए यह एक शक्तिशाली उपकरण है।</li>
                        <li class="hindi-font"><span class="font-semibold">वीडियो बनाम छवि:</span> वीडियो जनरेशन के लिए कैमरा मूवमेंट और समय के साथ क्रियाओं जैसे अतिरिक्त, गतिशील तत्वों की आवश्यकता होती है।</li>
                        <li class="hindi-font"><span class="font-semibold">संरचना का प्रयोग करें:</span> एक स्पष्ट संरचना (जैसे शॉट प्रकार + चरित्र + क्रिया + स्थान) का पालन करने से एआई को प्रॉम्प्ट को बेहतर ढंग से समझने में मदद मिलती है।</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>

    <script>
        const promptData = [
            {
                id: 1,
                title: "सुंदर महिला का पोर्ट्रेट",
                category: "पोर्ट्रेट",
                original: "एक सुंदर महिला का चित्र",
                optimized: "एक आकर्षक 25 वर्षीय महिला का सिनेमैटिक पोर्ट्रेट, प्राकृतिक मेकअप के साथ, मुलायम प्राकृतिक प्रकाश में, 85mm लेंस, उथली गहराई के साथ, फिल्म ग्रेन टेक्सचर, 8K UHD गुणवत्ता",
                justification: "मूल प्रॉम्प्ट में विशिष्टता की कमी थी। अनुकूलित संस्करण में उम्र, प्रकाश की गुणवत्ता, तकनीकी पैरामीटर और विस्तृत विवरण जोड़े गए हैं।"
            },
            {
                id: 2,
                title: "प्राकृतिक दृश्य",
                category: "प्रकृति",
                original: "एक खूबसूरत पहाड़ी दृश्य",
                optimized: "राजसी हिमालयी चोटियों का विहंगम दृश्य, सुनहरे घंटे की रोशनी में, नीचे हरी घाटियां, क्रिस्टल साफ झील, नाटकीय बादल, वाइड एंगल 24mm लेंस, HDR फोटोग्राफी, विविड रंग, 8K रिज़ॉल्यूशन",
                justification: "सामान्य 'खूबसूरत पहाड़ी दृश्य' को विशिष्ट स्थान, प्रकाश की स्थिति, संरचना और तकनीकी विवरण के साथ बेहतर बनाया गया।"
            },
            {
                id: 3,
                title: "शहरी वास्तुकला",
                category: "वास्तुकला",
                original: "एक आधुनिक इमारत",
                optimized: "भविष्यवादी गगनचुंबी इमारत, कांच और स्टील की संरचना, ज्यामितीय पैटर्न, नीली घंटे की रोशनी में, शहरी स्काईलाइन, तिर्यक कोण, वास्तुशिल्प फोटोग्राफी, 16-35mm लेंस, लंबा एक्सपोज़र, 8K UHD",
                justification: "मूल प्रॉम्प्ट बहुत सामान्य था। अनुकूलित संस्करण में सामग्री, शैली, प्रकाश, कोण और फोटोग्राफी तकनीक के विशिष्ट विवरण शामिल हैं।"
            },
            {
                id: 4,
                title: "जानवरों का चित्रण",
                category: "जानवर",
                original: "एक बाघ की तस्वीर",
                optimized: "राजसी बंगाली बाघ का क्लोज़-अप पोर्ट्रेट, तीव्र नारंगी आंखें, जंगली वातावरण में, प्राकृतिक धुंधली पृष्ठभूमि, 200mm टेलीफोटो लेंस, तेज़ फोकस, वन्यजीव फोटोग्राफी शैली, 8K UHD, नेशनल जियोग्राफिक गुणवत्ता",
                justification: "सामान्य 'बाघ की तस्वीर' को प्रजाति, शारीरिक विशेषताएं, वातावरण और पेशेवर वन्यजीव फोटोग्राफी तकनीकों के साथ विस्तृत किया गया।"
            },
            {
                id: 5,
                title: "खाना और पेय",
                category: "भोजन",
                original: "स्वादिष्ट खाना",
                optimized: "गर्म इतालवी पास्ता की कलात्मक प्लेटिंग, ताज़ी बेसिल पत्तियां, परमेसन चीज़, गर्म भाप उठती हुई, रेस्तरां की मुलायम रोशनी, मैक्रो लेंस 100mm, फूड स्टाइलिंग, गैस्ट्रोनॉमी फोटोग्राफी, 8K UHD",
                justification: "अस्पष्ट 'स्वादिष्ट खाना' को विशिष्ट व्यंजन, सामग्री, प्रस्तुति और फूड फोटोग्राफी तकनीकों के साथ परिष्कृत किया गया।"
            },
            {
                id: 6,
                title: "फैशन मॉडल",
                category: "फैशन",
                original: "फैशन मॉडल की फोटो",
                optimized: "एलिगेंट फैशन मॉडल, डिज़ाइनर गाउन में, स्टूडियो लाइटिंग सेटअप, प्रोफेशनल मेकअप, पोज़िंग, मिनिमलिस्ट बैकग्राउंड, 85mm पोर्ट्रेट लेंस, सॉफ्ट बॉक्स लाइटिंग, हाई फैशन फोटोग्राफी, वोग स्टाइल, 8K UHD",
                justification: "सामान्य 'फैशन मॉडल की फोटो' को कपड़ों के प्रकार, लाइटिंग सेटअप, पोज़िंग और पेशेवर फैशन फोटोग्राफी मानकों के साथ उन्नत किया गया।"
            },
            {
                id: 7,
                title: "कार की तस्वीर",
                category: "वाहन",
                original: "एक तेज़ कार",
                optimized: "लक्जरी स्पोर्ट्स कार, मेटैलिक रेड पेंट, चमकदार फिनिश, शहरी पार्किंग में, सुनहरे घंटे की रोशनी, गतिशील कोण, ऑटोमोटिव फोटोग्राफी, 24-70mm लेंस, रिफ्लेक्शन डिटेल्स, 8K UHD",
                justification: "अस्पष्ट 'तेज़ कार' को कार के प्रकार, रंग, फिनिश, स्थान और ऑटोमोटिव फोटोग्राफी तकनीकों के साथ विशिष्ट बनाया गया।"
            },
            {
                id: 8,
                title: "समुद्री दृश्य",
                category: "प्रकृति",
                original: "समुद्र की लहरें",
                optimized: "शक्तिशाली समुद्री लहरें चट्टानों से टकराती हुई, नाटकीय स्प्रे, तूफानी आकाश, गहरे नीले पानी, लंबा एक्सपोज़र फोटोग्राफी, 16-35mm वाइड एंगल लेंस, ND फिल्टर, सीस्केप फोटोग्राफी, 8K UHD",
                justification: "सामान्य 'समुद्र की लहरें' को गति, वातावरण, मौसम की स्थिति और समुद्री फोटोग्राफी तकनीकों के साथ जीवंत बनाया गया।"
            },
            {
                id: 9,
                title: "बच्चों का चित्र",
                category: "पोर्ट्रेट",
                original: "खुश बच्चे",
                optimized: "मासूम 5 साल के बच्चे का कैंडिड पोर्ट्रेट, प्राकृतिक मुस्कान, खेल के दौरान, मुलायम प्राकृतिक रोशनी, पार्क की पृष्ठभूमि, 85mm लेंस, उथली DOF, फैमिली फोटोग्राफी स्टाइल, 8K UHD",
                justification: "सामान्य 'खुश बच्चे' को उम्र, भावना, गतिविधि, वातावरण और बाल फोटोग्राफी तकनीकों के साथ व्यक्तिगत बनाया गया।"
            },
            {
                id: 10,
                title: "फूलों का बगीचा",
                category: "प्रकृति",
                original: "रंगबिरंगे फूल",
                optimized: "जीवंत ट्यूलिप गार्डन, वसंत ऋतु में, विविध रंगों की पंक्तियां, सुबह की ओस की बूंदें, मुलायम सुनहरी रोशनी, मैक्रो फोटोग्राफी, 100mm लेंस, बोकेह इफेक्ट, बॉटैनिकल फोटोग्राफी, 8K UHD",
                justification: "अस्पष्ट 'रंगबिरंगे फूल' को फूल के प्रकार, मौसम, व्यवस्था, प्राकृतिक तत्व और बॉटैनिकल फोटोग्राफी विशेषज्ञता के साथ समृद्ध किया गया।"
            },
            {
                id: 11,
                title: "रात का शहर",
                category: "शहरी",
                original: "रात में शहर",
                optimized: "चमकदार नियॉन लाइट्स के साथ रात्रिकालीन शहरी दृश्य, व्यस्त सड़कें, कार की हेडलाइट्स के ट्रेल्स, गगनचुंबी इमारतें, नाइट फोटोग्राफी, लंबा एक्सपोज़र, 24mm वाइड एंगल, शहरी जीवन, 8K UHD",
                justification: "सामान्य 'रात में शहर' को प्रकाश के स्रोत, गतिविधि, वास्तुकला और नाइट फोटोग्राफी तकनीकों के साथ गतिशील बनाया गया।"
            },
            {
                id: 12,
                title: "पारंपरिक कला",
                category: "कला",
                original: "पारंपरिक पेंटिंग",
                optimized: "जटिल मुगल मिनिएचर पेंटिंग, सुनहरे और नीले रंगों में, हाथ से बनी, पारंपरिक मोटिफ्स, विस्तृत बॉर्डर डिज़ाइन, कलात्मक विरासत, म्यूज़ियम गुणवत्ता, आर्ट फोटोग्राफी, 8K UHD",
                justification: "अस्पष्ट 'पारंपरिक पेंटिंग' को कला शैली, रंग पैलेट, तकनीक, डिज़ाइन तत्व और सांस्कृतिक संदर्भ के साथ विशिष्ट बनाया गया।"
            },
            {
                id: 13,
                title: "खेल की तस्वीर",
                category: "खेल",
                original: "फुटबॉल खेल",
                optimized: "गतिशील फुटबॉल एक्शन शॉट, खिलाड़ी गेंद को किक करते हुए, स्टेडियम की भीड़, नाटकीय लाइटिंग, फ्रीज़ मोशन, स्पोर्ट्स फोटोग्राफी, 200mm टेलीफोटो लेंस, तेज़ शटर स्पीड, 8K UHD",
                justification: "सामान्य 'फुटबॉल खेल' को विशिष्ट एक्शन, वातावरण, दर्शक और स्पोर्ट्स फोटोग्राफी तकनीकों के साथ रोमांचक बनाया गया।"
            },
            {
                id: 14,
                title: "मिठाई की तस्वीर",
                category: "भोजन",
                original: "मिठाई",
                optimized: "हस्तनिर्मित चॉकलेट ट्रफल्स, कलात्मक प्लेटिंग, सुनहरी डस्टिंग, एलिगेंट प्रेज़ेंटेशन, मुलायम स्टूडियो लाइटिंग, मैक्रो लेंस, फूड स्टाइलिंग, पेस्ट्री आर्ट, 8K UHD",
                justification: "सामान्य 'मिठाई' को मिठाई के प्रकार, तैयारी विधि, प्रस्तुति और पेस्ट्री फोटोग्राफी विशेषज्ञता के साथ परिष्कृत किया गया।"
            },
            {
                id: 15,
                title: "वन्यजीव फोटो",
                category: "जानवर",
                original: "जंगली जानवर",
                optimized: "अफ्रीकी सफारी में शेर का राजसी पोर्ट्रेट, सुनहरी अयाल, तीव्र निगाह, प्राकृतिक आवास, सवाना की पृष्ठभूमि, वन्यजीव फोटोग्राफी, 400mm सुपर टेलीफोटो, प्राकृतिक प्रकाश, 8K UHD",
                justification: "अस्पष्ट 'जंगली जानवर' को प्रजाति, स्थान, शारीरिक विशेषताएं, आवास और वन्यजीव फोटोग्राफी विशेषज्ञता के साथ जीवंत बनाया गया।"
            },
            {
                id: 16,
                title: "फैशन शो",
                category: "फैशन",
                original: "फैशन शो की तस्वीर",
                optimized: "हाई फैशन रनवे शो, मॉडल डिज़ाइनर गाउन में वॉकिंग, ड्रामैटिक स्टेज लाइटिंग, दर्शकों की भीड़, फैशन वीक इवेंट, प्रोफेशनल कैटवॉक, इवेंट फोटोग्राफी, 70-200mm लेंस, 8K UHD",
                justification: "सामान्य 'फैशन शो की तस्वीर' को इवेंट के प्रकार, मॉडल की गतिविधि, वातावरण, दर्शक और इवेंट फोटोग्राफी तकनीकों के साथ व्यापक बनाया गया।"
            },
            {
                id: 17,
                title: "पहाड़ी रास्ता",
                category: "प्रकृति",
                original: "पहाड़ों में रास्ता",
                optimized: "घुमावदार पहाड़ी सड़क, हरे भरे पेड़ों से घिरी, सुबह की धुंध, दूर तक फैली घाटी, ट्रैकिंग पथ, एडवेंचर फोटोग्राफी, वाइड एंगल 16-35mm, लैंडस्केप फोटोग्राफी, 8K UHD",
                justification: "सामान्य 'पहाड़ों में रास्ता' को सड़क के प्रकार, आसपास का वातावरण, मौसम की स्थिति और एडवेंचर फोटोग्राफी तकनीकों के साथ साहसिक बनाया गया।"
            },
            {
                id: 18,
                title: "शादी की तस्वीर",
                category: "इवेंट",
                original: "शादी की फोटो",
                optimized: "भारतीय शादी का पारंपरिक समारोह, दुल्हन और दुल्हे की रस्में, रंगबिरंगी सजावट, मेहंदी और गहने, खुशी के पल, वेडिंग फोटोग्राफी, 85mm पोर्ट्रेट लेंस, प्राकृतिक रोशनी, 8K UHD",
                justification: "सामान्य 'शादी की फोटो' को सांस्कृतिक संदर्भ, विशिष्ट रस्में, सजावट, भावनाएं और वेडिंग फोटोग्राफी विशेषज्ञता के साथ भावनात्मक बनाया गया।"
            },
            {
                id: 19,
                title: "तकनीकी गैजेट",
                category: "तकनीक",
                original: "नया फोन",
                optimized: "अत्याधुनिक स्मार्टफोन, स्लीक डिज़ाइन, मेटैलिक फिनिश, स्क्रीन डिस्प्ले ऑन, मिनिमलिस्ट बैकग्राउंड, प्रोडक्ट फोटोग्राफी, स्टूडियो लाइटिंग, 100mm मैक्रो लेंस, टेक फोटोग्राफी, 8K UHD",
                justification: "सामान्य 'नया फोन' को डिज़ाइन विशेषताएं, सामग्री, स्थिति, प्रस्तुति और प्रोडक्ट फोटोग्राफी तकनीकों के साथ आकर्षक बनाया गया।"
            },
            {
                id: 20,
                title: "कॉफी की तस्वीर",
                category: "भोजन",
                original: "कॉफी का कप",
                optimized: "आर्टिसनल कॉफी कप, लट्टे आर्ट डिज़ाइन, गर्म भाप उठती हुई, रस्टिक वुडन टेबल, कॉफी बीन्स बिखरी हुई, कैफे एम्बिएंस, बेवरेज फोटोग्राफी, 50mm लेंस, प्राकृतिक रोशनी, 8K UHD",
                justification: "सामान्य 'कॉफी का कप' को कॉफी के प्रकार, कलात्मक प्रस्तुति, वातावरण, सहायक तत्व और बेवरेज फोटोग्राफी विशेषज्ञता के साथ आकर्षक बनाया गया।"
            }
        ];

        // Get unique categories
        const categories = [...new Set(promptData.map(prompt => prompt.category))];

        // Chart.js configuration
        let categoryChart;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            setupNavigation();
            setupChart();
            setupFilters();
            renderPromptList();
        });

        // Setup navigation between sections
        function setupNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.content-section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.dataset.target;

                    // Remove active class from all nav links and sections
                    navLinks.forEach(nav => nav.classList.remove('active'));
                    sections.forEach(section => section.classList.remove('active'));

                    // Add active class to clicked nav link and corresponding section
                    this.classList.add('active');
                    document.getElementById(targetId).classList.add('active');
                });
            });
        }

        // Setup Chart.js bar chart
        function setupChart() {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            const categoryCounts = categories.map(category =>
                promptData.filter(prompt => prompt.category === category).length
            );

            categoryChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: categories,
                    datasets: [{
                        label: 'प्रॉम्प्ट्स की संख्या',
                        data: categoryCounts,
                        backgroundColor: [
                            '#14b8a6', '#0d9488', '#0f766e', '#134e4a',
                            '#06b6d4', '#0891b2', '#0e7490', '#155e75'
                        ],
                        borderColor: '#0f766e',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const category = categories[index];
                            filterByCategory(category);
                            // Switch to explorer section
                            document.querySelector('[data-target="explorer"]').click();
                        }
                    }
                }
            });
        }

        // Setup filter buttons
        function setupFilters() {
            const filterContainer = document.getElementById('filter-buttons');

            // Add "All" button
            const allButton = document.createElement('button');
            allButton.textContent = 'सभी';
            allButton.className = 'px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors';
            allButton.addEventListener('click', () => filterByCategory('all'));
            filterContainer.appendChild(allButton);

            // Add category buttons
            categories.forEach(category => {
                const button = document.createElement('button');
                button.textContent = category;
                button.className = 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors';
                button.addEventListener('click', () => filterByCategory(category));
                filterContainer.appendChild(button);
            });
        }

        // Filter prompts by category
        function filterByCategory(category) {
            const filteredData = category === 'all' ? promptData :
                promptData.filter(prompt => prompt.category === category);
            renderPromptList(filteredData);

            // Update button states
            const buttons = document.querySelectorAll('#filter-buttons button');
            buttons.forEach(btn => {
                btn.className = 'px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors';
            });

            const activeButton = Array.from(buttons).find(btn =>
                btn.textContent === (category === 'all' ? 'सभी' : category)
            );
            if (activeButton) {
                activeButton.className = 'px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors';
            }
        }

        // Render prompt list
        function renderPromptList(data = promptData) {
            const listContainer = document.getElementById('prompt-list');
            listContainer.innerHTML = '';

            data.forEach(prompt => {
                const promptCard = document.createElement('div');
                promptCard.className = 'prompt-card p-4 bg-white rounded-lg shadow-sm border cursor-pointer';
                promptCard.innerHTML = `
                    <h4 class="font-semibold text-slate-800 mb-2 hindi-font">${prompt.title}</h4>
                    <p class="text-sm text-slate-600 hindi-font">${prompt.category}</p>
                `;
                promptCard.addEventListener('click', () => showPromptDetail(prompt));
                listContainer.appendChild(promptCard);
            });
        }

        // Show prompt detail
        function showPromptDetail(prompt) {
            const placeholder = document.getElementById('prompt-detail-placeholder');
            const content = document.getElementById('prompt-detail-content');

            placeholder.classList.add('hidden');
            content.classList.remove('hidden');

            document.getElementById('detail-title').textContent = prompt.title;
            document.getElementById('detail-original').textContent = prompt.original;
            document.getElementById('detail-optimized').innerHTML = highlightChanges(prompt.original, prompt.optimized);
            document.getElementById('detail-justification').textContent = prompt.justification;
        }

        // Highlight changes in optimized prompt
        function highlightChanges(original, optimized) {
            // Simple highlighting - in a real implementation, you'd use a more sophisticated diff algorithm
            const originalWords = original.toLowerCase().split(' ');
            const optimizedWords = optimized.split(' ');

            return optimizedWords.map(word => {
                const cleanWord = word.toLowerCase().replace(/[^\w\u0900-\u097F]/g, '');
                if (!originalWords.some(origWord => origWord.replace(/[^\w\u0900-\u097F]/g, '') === cleanWord)) {
                    return `<span class="highlight-new">${word}</span>`;
                }
                return word;
            }).join(' ');
        }
    </script>
</body>
</html>