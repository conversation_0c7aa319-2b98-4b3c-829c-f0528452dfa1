<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>इंटरैक्टिव प्रॉम्प्ट ऑप्टिमाइज़ेशन रिपोर्ट</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Tiro+Devanagari+Hindi:wght@400&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Calm Harmony (Light Amber/Gray, Slate Gray, Muted Teal) -->
    <!-- Application Structure Plan: The application uses a tabbed navigation structure to break down the dense report into four manageable sections: Home, Guide, Explorer, and Learnings. This non-linear design avoids overwhelming the user with a long scroll. The core of the app is the 'Prompt Explorer', which uses interactive filters (buttons and a chart) and a side-by-side comparison view. This structure was chosen because the report's main goal is to compare original vs. optimized prompts, and this design directly facilitates that comparison, making it highly usable and focused on the user's primary task. -->
    <!-- Visualization & Content Choices: Report Info: Prompt categories -> Goal: Organize/Navigate -> Viz: Interactive Bar Chart (Chart.js) -> Interaction: Clicking a bar filters the prompt list, providing a visual entry point to the data. Report Info: Prompt Engineering Principles -> Goal: Inform -> Presentation: Expandable cards -> Interaction: Click to reveal details, keeping the initial view clean. Report Info: Original vs. Optimized Prompts -> Goal: Compare/Analyze -> Presentation: Side-by-side text blocks -> Interaction: JavaScript highlights new/changed words in the optimized prompt, making differences immediately obvious. This is more effective than static text. Report Info: Key Learnings -> Goal: Summarize -> Presentation: Styled list -> Interaction: Static, clear presentation of key takeaways. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', 'Tiro Devanagari Hindi', sans-serif;
        }
        .hindi-font {
            font-family: 'Tiro Devanagari Hindi', serif;
        }
        .nav-link {
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .nav-link.active {
            border-bottom-color: #0d9488; /* teal-600 */
            color: #0d9488;
        }
        .prompt-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .prompt-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
        .highlight-new {
            background-color: #ccfbf1; /* teal-100 */
            padding: 1px 3px;
            border-radius: 4px;
            font-weight: 500;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-slate-800">

    <div id="app" class="container mx-auto p-4 md:p-8">
        <header class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-teal-700 hindi-font">एआई प्रॉम्प्ट ऑप्टिमाइज़ेशन रिपोर्ट</h1>
            <p class="mt-4 text-lg text-slate-600">एआई-जनित छवियों और वीडियो के लिए प्रॉम्प्ट को बेहतर बनाने के लिए एक इंटरैक्टिव गाइड।</p>
        </header>

        <nav class="flex justify-center border-b border-gray-200 mb-8">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500">
                <li class="mr-2">
                    <a href="#home" class="nav-link inline-block p-4 rounded-t-lg active" data-target="home">
                        <span class="hindi-font text-lg">मुख्य पृष्ठ</span>
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#guide" class="nav-link inline-block p-4 rounded-t-lg" data-target="guide">
                         <span class="hindi-font text-lg">सिद्धांत</span>
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#explorer" class="nav-link inline-block p-4 rounded-t-lg" data-target="explorer">
                         <span class="hindi-font text-lg">प्रॉम्प्ट एक्सप्लोरर</span>
                    </a>
                </li>
                <li class="mr-2">
                    <a href="#learnings" class="nav-link inline-block p-4 rounded-t-lg" data-target="learnings">
                         <span class="hindi-font text-lg">मुख्य सीख</span>
                    </a>
                </li>
            </ul>
        </nav>

        <main>
            <!-- Home Section -->
            <section id="home" class="content-section active">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-4 hindi-font">रिपोर्ट का परिचय</h2>
                    <p class="text-slate-700 leading-relaxed hindi-font">
                        यह रिपोर्ट एआई-आधारित छवि और वीडियो जनरेशन के लिए उपयोगकर्ता द्वारा प्रदान किए गए प्रॉम्प्ट्स के गहन विश्लेषण, त्रुटि सुधार और अनुकूलन पर केंद्रित है। इसका प्राथमिक उद्देश्य इन प्रॉम्प्ट्स को "पूरी तरह से अनुकूलित" करना है, यह सुनिश्चित करते हुए कि वे एआई मॉडल्स से उच्च-गुणवत्ता वाले, सटीक और रचनात्मक आउटपुट उत्पन्न करें। यह इंटरैक्टिव एप्लिकेशन आपको रिपोर्ट के मुख्य निष्कर्षों को आसानी से नेविगेट करने और समझने में मदद करने के लिए डिज़ाइन किया गया है।
                    </p>
                    <div class="mt-8">
                        <h3 class="text-2xl font-bold text-teal-600 mb-4 hindi-font">प्रॉम्प्ट श्रेणियों का अवलोकन</h3>
                        <p class="text-slate-700 mb-4 hindi-font">रिपोर्ट में प्रॉम्प्ट्स को चार मुख्य श्रेणियों में बांटा गया है। नीचे दिया गया चार्ट प्रत्येक श्रेणी में प्रॉम्प्ट्स की संख्या को दर्शाता है। श्रेणियों का पता लगाने के लिए चार्ट पर क्लिक करें।</p>
                        <div class="chart-container bg-white p-4 rounded-lg shadow-inner">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Guide Section -->
            <section id="guide" class="content-section">
                 <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-6 hindi-font">प्रभावी प्रॉम्प्ट इंजीनियरिंग के सिद्धांत</h2>
                    <div class="space-y-4">
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">मुख्य घटक</h3>
                            <p class="text-slate-600 hindi-font">एक प्रभावी प्रॉम्प्ट में स्पष्ट घटक होने चाहिए: विषय, क्रिया, वातावरण, माध्यम/शैली, प्रकाश, रंग, मनोदशा और संरचना।</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">विशिष्टता का महत्व</h3>
                            <p class="text-slate-600 hindi-font">जितना अधिक विशिष्ट और विस्तृत प्रॉम्प्ट होगा, एआई मॉडल द्वारा वांछित परिणाम उत्पन्न करने की संभावना उतनी ही अधिक होगी। 'बड़ा' के बजाय 'विशाल' जैसे विशिष्ट पर्यायवाची शब्दों का प्रयोग करें।</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">तकनीकी पैरामीटर्स</h3>
                            <p class="text-slate-600 hindi-font">8K UHD, DSLR 85mm लेंस, सिनेमैटिक कलर ग्रेडिंग जैसे तकनीकी विवरण शामिल करने से गुणवत्ता और शैली में काफी सुधार होता है।</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h3 class="font-semibold text-xl text-slate-800 mb-2 hindi-font">नकारात्मक प्रॉम्प्ट्स</h3>
                            <p class="text-slate-600 hindi-font">अवांछित तत्वों, बनावटों या विशेषताओं को बाहर करने के लिए `--neg` या इसी तरह के कमांड का उपयोग करें। यह आउटपुट को परिष्कृत करने में मदद करता है।</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Explorer Section -->
            <section id="explorer" class="content-section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-2 hindi-font">प्रॉम्प्ट एक्सप्लोरर</h2>
                    <p class="text-slate-600 mb-6 hindi-font">श्रेणी के अनुसार प्रॉम्प्ट्स को फ़िल्टर करें और प्रत्येक के मूल और अनुकूलित संस्करणों की तुलना करें। विवरण देखने के लिए किसी भी प्रॉम्प्ट शीर्षक पर क्लिक करें।</p>
                    
                    <div id="filter-buttons" class="flex flex-wrap justify-center gap-2 mb-8">
                        <!-- Filter buttons will be generated here -->
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-12 gap-8">
                        <div id="prompt-list-container" class="md:col-span-4">
                            <h3 class="text-xl font-semibold mb-4 text-slate-700 hindi-font">प्रॉम्प्ट्स</h3>
                            <div id="prompt-list" class="space-y-2 max-h-[600px] overflow-y-auto pr-2">
                                <!-- Prompt list will be generated here -->
                            </div>
                        </div>
                        <div id="prompt-detail-container" class="md:col-span-8 bg-gray-50 p-6 rounded-lg">
                            <div id="prompt-detail-placeholder" class="text-center text-slate-500 flex items-center justify-center h-full">
                                <p class="hindi-font">तुलना देखने के लिए बाईं ओर से एक प्रॉम्प्ट चुनें।</p>
                            </div>
                            <div id="prompt-detail-content" class="hidden">
                                <h3 id="detail-title" class="text-2xl font-bold text-teal-700 mb-4 hindi-font"></h3>
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div>
                                        <h4 class="text-lg font-semibold mb-2 border-b-2 border-red-300 pb-1 hindi-font">मूल प्रॉम्प्ट</h4>
                                        <p id="detail-original" class="text-sm text-slate-600 leading-relaxed bg-red-50 p-3 rounded-md hindi-font"></p>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-semibold mb-2 border-b-2 border-green-300 pb-1 hindi-font">अनुकूलित प्रॉम्प्ट</h4>
                                        <p id="detail-optimized" class="text-sm text-slate-700 leading-relaxed bg-green-50 p-3 rounded-md hindi-font"></p>
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <h4 class="text-lg font-semibold mb-2 border-b-2 border-blue-300 pb-1 hindi-font">परिवर्तनों का औचित्य</h4>
                                    <p id="detail-justification" class="text-sm text-slate-600 leading-relaxed bg-blue-50 p-3 rounded-md hindi-font"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Learnings Section -->
            <section id="learnings" class="content-section">
                <div class="bg-white p-6 md:p-8 rounded-xl shadow-sm">
                    <h2 class="text-3xl font-bold text-teal-600 mb-6 hindi-font">मुख्य सीख और सिफारिशें</h2>
                    <ul class="space-y-4 list-disc list-inside text-slate-700">
                        <li class="hindi-font"><span class="font-semibold">पुनरावृत्ति महत्वपूर्ण है:</span> वांछित परिणाम प्राप्त करने के लिए प्रॉम्प्ट्स को लगातार परिष्कृत करने की आवश्यकता होती है।</li>
                        <li class="hindi-font"><span class="font-semibold">संक्षिप्तता बनाम विवरण:</span> एआई को स्पष्ट दिशा देने के लिए पर्याप्त विवरण प्रदान करें, लेकिन अनावश्यक शब्दों से बचें जो फोकस को कमजोर कर सकते हैं।</li>
                        <li class="hindi-font"><span class="font-semibold">विशिष्ट शब्दावली:</span> सटीक पर्यायवाची और ठोस भाषा का उपयोग करने से एआई की व्याख्या क्षमता में काफी वृद्धि होती है।</li>
                        <li class="hindi-font"><span class="font-semibold">नकारात्मक प्रॉम्प्ट्स:</span> अवांछित तत्वों को फ़िल्टर करके आउटपुट को ठीक करने के लिए यह एक शक्तिशाली उपकरण है।</li>
                        <li class="hindi-font"><span class="font-semibold">वीडियो बनाम छवि:</span> वीडियो जनरेशन के लिए कैमरा मूवमेंट और समय के साथ क्रियाओं जैसे अतिरिक्त, गतिशील तत्वों की आवश्यकता होती है।</li>
                        <li class="hindi-font"><span class="font-semibold">संरचना का प्रयोग करें:</span> एक स्पष्ट संरचना (जैसे शॉट प्रकार + चरित्र + क्रिया + स्थान) का पालन करने से एआई को प्रॉम्प्ट को बेहतर ढंग से समझने में मदद मिलती है।</li>
                    </ul>
                </div>
            </section>
        </main>
    </div>

    <script>
        const promptData = [
            {
                id: 1,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "महल में कामुक आलिंगन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा चमकदार गोरी है... एक शक्तिशाली रूप से मांसल 35 वर्षीय भारतीय पुरुष के साथ अंतरंग रूप से एक साथ बैठे... सेटिंग: भव्य महल कक्ष...",
                optimized: "आई-लेवल शॉट, थर्ड्स का नियम, 8K UHD, हाइपर-रियलिस्टिक, DSLR 85mm लेंस, प्राकृतिक बोकेह प्रभाव, सिनेमैटिक कलर ग्रेडिंग, वॉल्यूमेट्रिक लाइटिंग, फोटोरियलिस्टिक, गतिशील कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष, जिसकी त्वचा दीप्तिमान गोरी और मोती जैसी गुणवत्ता के साथ भीतर से चमक रही है... शानदार जेट-काले, रेशमी बाल, कूल्हों तक लंबे, एक सरल, बड़ा, और काला जूड़ा (उपडो) में सुरुचिपूर्ण ढंग से स्टाइल किए गए हैं... वह सूक्ष्म फूलों की कढ़ाई (चेरी ब्लॉसम, तितलियाँ) और छोटे सेक्विन के साथ एक बहती हुई पेस्टल लैवेंडर रेशमी शिफॉन पोशाक पहने हुए है... हवा में समृद्ध चंदन की तीव्र सुगंध, जो कामुकता को और गहरा करती है। मधुर शास्त्रीय बांसुरी की धुनें वातावरण में गूंजती हुई...",
                justification: "वीडियो अनुकूलन के लिए 'गतिशील कैमरा पैनिंग' जोड़ा गया। दृश्य गुणवत्ता बढ़ाने के लिए 'सिनेमैटिक कलर ग्रेडिंग', 'वॉल्यूमेट्रिक लाइटिंग' और 'फोटोरियलिस्टिक' जैसे तकनीकी पैरामीटर जोड़े गए। संवेदी अनुभव को तीव्र करने के लिए गंध और ध्वनि के विवरण को बढ़ाया गया।"
            },
            {
                id: 2,
                category: "सामान्य अंतरंगता और भावनात्मक संबंध",
                title: "उत्सव का आलिंगन",
                original: "एक 30 वर्षीय भारतीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष त्योहार के कमरे में आलिंगनबद्ध हैं...",
                optimized: "लो-एंगल शॉट, तीव्र विषय, धुंधली पृष्ठभूमि, 8K UHD, सिनेमैटिक कलर ग्रेडिंग, फोटोरियलिस्टिक, HDR, वॉल्यूमेट्रिक लाइटिंग, धीमी कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष... उसका व्यवहार कोमल शर्म के साथ bubbling आनंद का मिश्रण दर्शाता है... वह जटिल चांदी के धागे की कढ़ाई (कमल, मोर के रूपांकन) और एक समृद्ध सुनहरी बॉर्डर के साथ एक उत्कृष्ट पन्ना हरे रंग की रेशमी साड़ी पहने हुए है... कोमल, मधुर सितार की धुनें जो वातावरण को कामुक बनाती हैं। हवा में गुलाब जल की तीव्र सुगंध।",
                justification: "वीडियो के लिए 'धीमी कैमरा ट्रैकिंग' जोड़ा गया। प्रकाश और गहराई को बढ़ाने के लिए 'HDR' और 'वॉल्यूमेट्रिक लाइटिंग' को शामिल किया गया। भावनात्मक अभिव्यक्ति ('bubbling आनंद') और सेटिंग के विवरण (साड़ी, संगीत, सुगंध) को और अधिक विशिष्ट बनाया गया।"
            },
            {
                id: 3,
                category: "आभूषण केंद्रित",
                title: "पुस्तकालय में विद्वत्तापूर्ण जुड़ाव",
                original: "35 वर्षीय स्त्रैण पुरुष और 50 वर्षीय मांसल पुरुष पुस्तकालय में बैठे हैं, आभूषण पर जोर।",
                optimized: "सिनेमैटिक फोटोग्राफी, अल्ट्रा-डिटेल्ड रेंडर, यथार्थवादी प्रकाश व्यवस्था, 8K UHD, फोटोरियलिस्टिक, सॉफ्ट बोकेह, धीमी कैमरा पैनिंग। एक 35 वर्षीय भारतीय स्त्रैण पुरुष... उसके शानदार समृद्ध शाहबलूत भूरे रंग के बाल... एक शानदार सोने के मोर के हेयरपिन से सजे हुए हैं जिसमें एक शानदार रूबी कैबोचोन जड़ा हुआ है... पारंपरिक चांदी के झुमके, एक केंद्रीय चमकदार मोती के साथ नाजुक मांग टीका। सेटिंग: महोगनी की ऊंची अलमारियों से भरी शानदार पुरानी लाइब्रेरी... हवा में पुरानी कागज, चमड़े और पुराने चर्मपत्र की सुगंध घुल रही है...",
                justification: "वीडियो के लिए 'धीमी कैमरा पैनिंग' जोड़ा गया। आभूषणों के विवरण को और अधिक विशिष्ट बनाया गया ('रूबी कैबोचोन', 'मांग टीका')। सेटिंग के संवेदी अनुभव को बढ़ाने के लिए प्रकाश और गंध के विवरण को विस्तृत किया गया।"
            },
            {
                id: 4,
                category: "आभूषण केंद्रित",
                title: "किले में शाही गरिमा",
                original: "30 वर्षीय स्त्रैण पुरुष और 38 वर्षीय मांसल पुरुष किले में खड़े हैं, आभूषण पर जोर।",
                optimized: "अति-यथार्थवादी सिनेमैटिक रेंडर, गहरी रंग योजना, 8K UHD, फोटोरियलिस्टिक, डायनामिक कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष... उसके माथे पर एक नाजुक लाल बिंदी सजी है... एक नाजुक चांदी के अर्धचंद्र हेयर क्लिप और शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन से सजे हुए... एक छोटे लटकते मोती के साथ पारंपरिक सोने की नथ। सेटिंगः प्राचीन किले के शानदार प्रांगण में... हल्की रेगिस्तानी हवा रेगिस्तानी फूलों और चमेली की सुगंध लाती है...",
                justification: "वीडियो के लिए 'डायनामिक कैमरा ट्रैकिंग' जोड़ा गया। आभूषणों ('नीलमणि हेयर चेन', 'सोने की नथ') और चरित्र ('लाल बिंदी') के विवरण को बढ़ाया गया। सेटिंग में संवेदी विवरण (हवा, सुगंध) को जोड़ा गया।"
            },
            {
                id: 5,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "कामुक इच्छा का आलिंगन",
                original: "30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से जुड़े हुए हैं।",
                optimized: "क्लोज-अप शॉट, 8K UHD, DSLR 50mm लेंस, हाइपर-रियलिस्टिक... धीमी ज़ूम-इन गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष... उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: तीखी, कोणीय जबड़े की रेखा... फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। सूक्ष्म त्वचा बनावट, पोर और प्राकृतिक खामियों के साथ यथार्थवादी त्वचा... शानदार जेट-काले रेशमी बाल... एक नाजुक सुनहरी माला से सजे हुए जिसमें छोटे मोती जड़े हैं। वह पूरी तरह से नग्न है... सेटिंग: ...पीतल के दीयों और खंभे वाली मोमबत्तियों से गर्म, सुनहरी, सिनेमाई रोशनी, जो शरीर पर नरम छाया और हाइलाइट्स बनाती है।",
                justification: "'मर्दाना चेहरे की बनावट' के विरोधाभास को स्पष्ट रूप से संबोधित किया गया ताकि एआई वांछित मिश्रण उत्पन्न कर सके। यथार्थवाद बढ़ाने के लिए 'यथार्थवादी त्वचा बनावट' को जोड़ा गया। वीडियो के लिए 'धीमी ज़ूम-इन गति' और बेहतर दृश्य के लिए प्रकाश व्यवस्था का विस्तृत विवरण शामिल किया गया।"
            },
             {
                id: 6,
                category: "स्पष्ट रूप से कामुक/यौन विषय",
                title: "अनकही कामुकता की प्रत्याशा",
                original: "30 वर्षीय स्त्रैण पुरुष और 35 वर्षीय मांसल पुरुष नग्न अवस्था में अंतरंगता से लेटे हुए हैं।",
                optimized: "क्लोज-अप शॉट, फोटोरियलिस्टिक, Canon EOS R5 से खींची गई तस्वीर... धीमी कैमरा पैनिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष... उसकी चेहरे की बनावट में मर्दाना तीक्ष्णता और स्त्रीत्व का अनूठा मिश्रण है: रेज़र-शार्प, छेनी वाली जबड़े की रेखा... फिर भी समग्र रूप से स्त्रैण सौंदर्य को बनाए रखती है। यथार्थवादी त्वचा बनावट, सूक्ष्म बालों और प्राकृतिक चमक के साथ... एक नाजुक चांदी के अर्धचंद्र हेयर क्लिप और शानदार नीलमणि से जड़ी एक उत्कृष्ट हेयर चेन से सजे हुए। वह पूरी तरह से नग्न है...",
                justification: "चेहरे की बनावट के विरोधाभास को स्पष्ट किया गया। यथार्थवाद के लिए 'Canon EOS R5' जैसे विशिष्ट कैमरा मॉडल को जोड़ा गया। त्वचा की बनावट और बालों के आभूषणों का विवरण बढ़ाया गया। वीडियो के लिए 'धीमी कैमरा पैनिंग' शामिल किया गया।"
            },
            {
                id: 7,
                category: "भावनात्मक संकट और सांत्वना",
                title: "गहरे दुख में सांत्वना",
                original: "30 वर्षीय स्त्रैण पुरुष गहन दुख में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "क्लोज-अप शॉट, फोटोरियलिस्टिक, 8K UHD, सिनेमैटिक लाइटिंग... धीमी ज़ूम-इन गति। एक 30 वर्षीय भारतीय स्त्रैण पुरुष... वह गहरी उदासी और भेद्यता के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसके गालों पर गर्म आँसू चमक रहे हैं, भौहें सिकुड़ी हुई हैं... दुख और पीड़ा की गहरी अभिव्यक्ति दर्शाते हुए... वह गहरे मैरून रेशमी लहंगा-चोली पहने हुए है... एक शक्तिशाली 35 वर्षीय भारतीय पुरुष... घुटनों पर बैठकर स्त्रैण पुरुष को सांत्वना दे रहा है... उसका एक हाथ कोमलता से उनके गाल पर आँसू पोंछ रहा है... जो अटूट समर्थन और सुरक्षा का संचार करता है।",
                justification: "भावनात्मक अभिव्यक्ति और शारीरिक भाषा ('गर्म आँसू चमक रहे हैं', 'आँसू पोंछ रहा है') को अधिक स्पष्ट किया गया। वीडियो के लिए 'धीमी ज़ूम-इन गति' जोड़ी गई। पोशाक और आभूषणों का विस्तृत वर्णन शामिल किया गया ताकि दृश्य को और समृद्ध बनाया जा सके।"
            },
            {
                id: 8,
                category: "भावनात्मक संकट और सांत्वना",
                title: "क्रोध में शक्तिहीनता",
                original: "30 वर्षीय स्त्रैण पुरुष क्रोध और निराशा में रो रहा है, 35 वर्षीय पुरुष उसे सांत्वना दे रहा है।",
                optimized: "वाइड शॉट, फोटोरियलिस्टिक, 8K UHD... धीमी कैमरा ट्रैकिंग। एक 30 वर्षीय भारतीय स्त्रैण पुरुष... वह तीव्र क्रोध और शक्तिहीन निराशा के साथ, महिलाओं की तरह भावनात्मक रूप से रो रहा है। उसकी आँखें चौड़ी और तनावपूर्ण, होंठ कसकर दबे, जबड़ा तना हुआ... तीव्र भावनात्मक उथल-पुथल दर्शाते हुए... एक शक्तिशाली 35 वर्षीय भारतीय पुरुष... अपनी बाहों से स्त्रैण पुरुष को कोमलता से सांत्वना दे रहा है... जो अटूट समर्थन और सुरक्षा का संचार करता है।",
                justification: "क्रोध और निराशा की भावनाओं को चेहरे के भावों और शारीरिक भाषा के माध्यम से अधिक स्पष्ट रूप से वर्णित किया गया है। वीडियो के लिए 'धीमी कैमरा ट्रैकिंग' को शामिल किया गया है। पोशाक और आभूषणों का विवरण जोड़ा गया है।"
            }
        ];

        document.addEventListener('DOMContentLoaded', function () {
            const navLinks = document.querySelectorAll('.nav-link');
            const contentSections = document.querySelectorAll('.content-section');
            const promptList = document.getElementById('prompt-list');
            const filterButtonsContainer = document.getElementById('filter-buttons');
            const promptDetailPlaceholder = document.getElementById('prompt-detail-placeholder');
            const promptDetailContent = document.getElementById('prompt-detail-content');
            
            const categories = [...new Set(promptData.map(p => p.category))];
            let activeFilter = 'All';

            function setupNavigation() {
                navLinks.forEach(link => {
                    link.addEventListener('click', function (e) {
                        e.preventDefault();
                        const targetId = this.dataset.target;

                        navLinks.forEach(l => l.classList.remove('active'));
                        contentSections.forEach(s => s.classList.remove('active'));

                        this.classList.add('active');
                        document.getElementById(targetId).classList.add('active');
                    });
                });
            }
            
            function createFilterButtons() {
                let buttonsHTML = `<button class="filter-btn bg-teal-500 text-white py-2 px-4 rounded-full text-sm" data-category="All">सभी श्रेणियाँ</button>`;
                categories.forEach(category => {
                    buttonsHTML += `<button class="filter-btn bg-white text-teal-600 border border-teal-500 py-2 px-4 rounded-full text-sm" data-category="${category}">${category}</button>`;
                });
                filterButtonsContainer.innerHTML = buttonsHTML;

                document.querySelectorAll('.filter-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        activeFilter = this.dataset.category;
                        updateFilterButtons();
                        renderPrompts();
                    });
                });
                updateFilterButtons();
            }

            function updateFilterButtons() {
                document.querySelectorAll('.filter-btn').forEach(button => {
                    if (button.dataset.category === activeFilter) {
                        button.classList.remove('bg-white', 'text-teal-600', 'border', 'border-teal-500');
                        button.classList.add('bg-teal-500', 'text-white');
                    } else {
                        button.classList.remove('bg-teal-500', 'text-white');
                        button.classList.add('bg-white', 'text-teal-600', 'border', 'border-teal-500');
                    }
                });
            }

            function renderPrompts() {
                const filteredPrompts = activeFilter === 'All' ? promptData : promptData.filter(p => p.category === activeFilter);
                promptList.innerHTML = filteredPrompts.map(prompt => `
                    <div class="prompt-card cursor-pointer p-3 bg-white border border-gray-200 rounded-lg hover:bg-teal-50" data-id="${prompt.id}">
                        <h4 class="font-semibold text-slate-800 hindi-font">${prompt.title}</h4>
                        <p class="text-xs text-slate-500 hindi-font">${prompt.category}</p>
                    </div>
                `).join('');

                document.querySelectorAll('.prompt-card').forEach(card => {
                    card.addEventListener('click', function() {
                        displayPromptDetails(this.dataset.id);
                    });
                });
            }

            function diffStrings(oldStr, newStr) {
                const oldWords = oldStr.split(/(\s+)/);
                const newWords = newStr.split(/(\s+)/);
                const oldWordsSet = new Set(oldWords);
                
                return newWords.map(word => {
                    if (!oldWordsSet.has(word) && word.trim() !== '') {
                        return `<span class="highlight-new">${word}</span>`;
                    }
                    return word;
                }).join('');
            }

            function displayPromptDetails(id) {
                const prompt = promptData.find(p => p.id == id);
                if (prompt) {
                    promptDetailPlaceholder.classList.add('hidden');
                    promptDetailContent.classList.remove('hidden');

                    document.getElementById('detail-title').innerText = prompt.title;
                    document.getElementById('detail-original').innerText = prompt.original;
                    document.getElementById('detail-optimized').innerHTML = diffStrings(prompt.original, prompt.optimized);
                    document.getElementById('detail-justification').innerText = prompt.justification;

                    document.querySelectorAll('.prompt-card').forEach(card => {
                        card.classList.remove('bg-teal-100', 'border-teal-400');
                    });
                    document.querySelector(`.prompt-card[data-id='${id}']`).classList.add('bg-teal-100', 'border-teal-400');
                }
            }

            function createCategoryChart() {
                const ctx = document.getElementById('categoryChart').getContext('2d');
                const categoryCounts = categories.map(cat => promptData.filter(p => p.category === cat).length);
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: categories.map(c => c.split(' ').map(w => w.charAt(0)).join('')), // Initials for labels
                        datasets: [{
                            label: 'प्रॉम्प्ट्स की संख्या',
                            data: categoryCounts,
                            backgroundColor: [
                                'rgba(20, 184, 166, 0.6)',
                                'rgba(245, 158, 11, 0.6)',
                                'rgba(239, 68, 68, 0.6)',
                                'rgba(59, 130, 246, 0.6)'
                            ],
                            borderColor: [
                                'rgba(13, 148, 136, 1)',
                                'rgba(217, 119, 6, 1)',
                                'rgba(220, 38, 38, 1)',
                                'rgba(37, 99, 235, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                beginAtZero: true
                            },
                            y: {
                                ticks: {
                                    callback: function(value, index, values) {
                                        return categories[index];
                                    },
                                    font: {
                                        family: "'Tiro Devanagari Hindi', serif",
                                        size: 14
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(context) {
                                        return categories[context[0].dataIndex];
                                    }
                                }
                            }
                        },
                        onClick: (e, elements) => {
                            if (elements.length > 0) {
                                const index = elements[0].index;
                                const category = categories[index];
                                activeFilter = category;
                                document.querySelector('.nav-link[data-target="explorer"]').click();
                                updateFilterButtons();
                                renderPrompts();
                            }
                        }
                    }
                });
            }

            setupNavigation();
            createFilterButtons();
            renderPrompts();
            createCategoryChart();
        });
    </script>
</body>
</html>
