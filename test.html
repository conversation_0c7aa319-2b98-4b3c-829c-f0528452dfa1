<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <h1 class="text-2xl font-bold mb-4">Test Page</h1>
    <div id="test-container" class="border border-gray-300 p-4 min-h-[200px]">
        <p>Container ready...</p>
    </div>
    
    <script>
        const testData = [
            { id: 1, title: "Test 1", category: "Test" },
            { id: 2, title: "Test 2", category: "Test" },
            { id: 3, title: "Test 3", category: "Test" }
        ];
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            console.log('Test data:', testData);
            
            const container = document.getElementById('test-container');
            console.log('Container found:', !!container);
            
            container.innerHTML = '';
            
            testData.forEach((item, index) => {
                console.log('Adding item:', item.title);
                const div = document.createElement('div');
                div.className = 'p-2 bg-white border mb-2';
                div.textContent = item.title + ' - ' + item.category;
                container.appendChild(div);
                console.log('Added item', index + 1);
            });
            
            console.log('Final container children count:', container.children.length);
        });
    </script>
</body>
</html>
